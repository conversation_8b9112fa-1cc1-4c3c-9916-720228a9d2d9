# External API Setup Guide

This guide explains how to set up the external API (port 3001) to handle user profiles and permissions using idToken authentication.

## Overview

The authentication system has been migrated to use `idToken` instead of `accessToken` for external API calls. The external API should be configured to:

1. Accept `idToken` in the `Authorization` header
2. Validate the JWT token
3. Return user profile data with permissions and roles

## API Endpoints Required

### GET /users/{userId}

**Authentication:** Bearer token (idToken) in Authorization header
**Parameters:**

- `userId`: User identifier (can be email or UUID)
- `orgId` (optional): Organization context

**Response:**

```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "name": "User Name",
  "username": "username",
  "permissions": [
    "global|org:*|users|read",
    "org:*|projects|read",
    "user:self|profile|read"
  ],
  "roles": ["org_member"],
  "organizations": [
    {
      "org": {
        "role": "admin",
        "email": "<EMAIL>",
        "organization": {
          "name": "My Organization",
          "_id": "org-uuid"
        }
      }
    }
  ],
  "currentOrganizationId": "org-uuid"
}
```

## Environment Variables

Set the following environment variables in your external API:

```bash
EXTERNAL_API_KEY=your-api-key-for-fallback-auth
JWT_SECRET=your-jwt-secret-for-token-validation
```

## Token Validation

The external API should:

1. Extract the `idToken` from the `Authorization: Bearer <idToken>` header
2. Decode and validate the JWT token
3. Extract user information from the token payload
4. Return appropriate user profile data

## Fallback Behavior

If the external API is unavailable, the system will fall back to mock user data for development purposes.

## Testing

To test the integration:

1. Start the external API on port 3001
2. Set the `EXTERNAL_API_URL` environment variable
3. Perform a login operation
4. Verify that the external API receives the idToken and returns user profile data

## Security Considerations

- Always validate JWT tokens before processing requests
- Use HTTPS in production
- Implement proper rate limiting
- Log authentication attempts for monitoring
