import React, { useMemo } from "react";
import { PermissionString } from "@/types/auth";
import {
    hasPermission,
    hasAnyPermission,
    hasPermissionEnhanced,
    hasAnyPermissionEnhanced,
    hasRole,
    hasAnyRole,
    hasPermissionsOrRoles,
    getEffectivePermissions,
    parsePermission,
    stringifyPermission,
} from "@/lib/permissions/permissions";
import { useAuth } from "@/components/auth/auth-provider";

/**
 * Hook for permission checking in React components
 */
export function usePermissions() {
    const { user } = useAuth();

    const userPermissions: PermissionString[] = user?.permissions || [];
    const userRoles: string[] = user?.roles || [];
    const organizationId: string | undefined = user?.organizationId;

    const effectivePermissions = useMemo(() => {
        return getEffectivePermissions(userPermissions, userRoles);
    }, [userPermissions, userRoles]);

    return {
        // Permission checking functions
        hasPermission: (permission: PermissionString) =>
            hasPermissionEnhanced(
                userPermissions,
                userRoles,
                permission,
                organizationId
            ).granted,

        hasAnyPermission: (permissions: PermissionString[]) =>
            hasAnyPermissionEnhanced(
                userPermissions,
                userRoles,
                permissions,
                organizationId
            ).granted,

        hasRole: (role: string) => hasRole(userRoles, role),

        hasAnyRole: (roles: string[]) => hasAnyRole(userRoles, roles),

        hasPermissionsOrRoles: (
            permissions: PermissionString[],
            roles: string[]
        ) =>
            hasPermissionsOrRoles(
                userPermissions,
                userRoles,
                permissions,
                roles,
                organizationId
            ).granted,

        // Data
        permissions: userPermissions,
        roles: userRoles,
        organizationId,
        effectivePermissions,

        // Utility functions
        parsePermission,
        stringifyPermission,
    };
}

/**
 * Hook for checking a specific permission
 */
export function usePermissionCheck(requiredPermission: PermissionString) {
    const { hasPermission } = usePermissions();
    return hasPermission(requiredPermission);
}

/**
 * Hook for checking multiple permissions (ANY)
 */
export function useAnyPermissionCheck(requiredPermissions: PermissionString[]) {
    const { hasAnyPermission } = usePermissions();
    return hasAnyPermission(requiredPermissions);
}

/**
 * Hook for checking multiple permissions (ALL)
 */
export function useAllPermissionsCheck(
    requiredPermissions: PermissionString[]
) {
    const { hasAnyPermission } = usePermissions(); // Note: Using ANY for now, you may want to implement ALL
    return hasAnyPermission(requiredPermissions);
}

/**
 * Hook for checking a specific role
 */
export function useRoleCheck(requiredRole: string) {
    const { hasRole } = usePermissions();
    return hasRole(requiredRole);
}

/**
 * Hook for checking multiple roles (ANY)
 */
export function useAnyRoleCheck(requiredRoles: string[]) {
    const { hasAnyRole } = usePermissions();
    return hasAnyRole(requiredRoles);
}

/**
 * Hook for conditional rendering based on permissions
 */
export function usePermissionGuard(
    permission?: PermissionString,
    permissions?: PermissionString[],
    role?: string,
    roles?: string[],
    requireAll = false
) {
    const { hasPermission, hasAnyPermission, hasRole, hasAnyRole } =
        usePermissions();

    return useMemo(() => {
        if (permission) {
            return hasPermission(permission);
        }

        if (permissions) {
            return requireAll
                ? hasAnyPermission(permissions) // Note: Using ANY for now, you may want to implement ALL
                : hasAnyPermission(permissions);
        }

        if (role) {
            return hasRole(role);
        }

        if (roles) {
            return hasAnyRole(roles);
        }

        return true; // No conditions specified
    }, [
        permission,
        permissions,
        role,
        roles,
        requireAll,
        hasPermission,
        hasAnyPermission,
        hasRole,
        hasAnyRole,
    ]);
}

/**
 * Higher-order component for permission-based rendering
 */
export function withPermissionGuard<P extends object>(
    WrappedComponent: React.ComponentType<P>,
    permissionOptions: {
        permission?: PermissionString;
        permissions?: PermissionString[];
        role?: string;
        roles?: string[];
        requireAll?: boolean;
        fallback?: React.ReactNode;
    }
) {
    const PermissionGuardedComponent: React.FC<P> = (props) => {
        const hasAccess = usePermissionGuard(
            permissionOptions.permission,
            permissionOptions.permissions,
            permissionOptions.role,
            permissionOptions.roles,
            permissionOptions.requireAll
        );

        if (!hasAccess) {
            return (permissionOptions.fallback as React.ReactElement) || null;
        }

        return React.createElement(WrappedComponent, props);
    };

    return PermissionGuardedComponent;
}

/**
 * Hook for getting permission-based UI state
 */
export function usePermissionUI() {
    const { permissions, roles, effectivePermissions } = usePermissions();

    return {
        // Check if user can perform admin actions
        canManageUsers: usePermissionCheck("global|org:*|users|manage"),
        canManageOrganization: usePermissionCheck(
            "global|org:*|organization|manage"
        ),
        canViewBilling: usePermissionCheck("global|org:*|billing|view"),
        canManageBilling: usePermissionCheck("global|org:*|billing|manage"),
        canViewAnalytics: usePermissionCheck("global|org:*|analytics|view"),
        canManageProjects: usePermissionCheck("global|org:*|projects|manage"),
        canExecuteJobs: usePermissionCheck("global|org:*|jobs|execute"),

        // Role-based checks
        isAdmin:
            hasRole(roles, "org_admin") || hasRole(roles, "platform_admin"),
        isManager: hasRole(roles, "org_manager"),
        isMember: hasRole(roles, "org_member"),

        // Permission lists for debugging
        permissions,
        roles,
        effectivePermissions,
    };
}
