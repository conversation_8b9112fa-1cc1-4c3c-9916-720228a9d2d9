"use client";

import { useState, useEffect } from 'react';
import { userAPI } from '@/lib/api/client/user-api';
import { authAPI } from '@/lib/api/client/auth-api';
import { apiClient } from '@/lib/api/api-client';
import { useAuth } from '@/components/auth/auth-provider';

/**
 * Example hook showing the complete flow:
 * 
 * CLIENT SIDE (React):
 * 1. UI Component → uses this hook
 * 2. Hook → calls apiClient (localhost:3000/api/...)
 * 3. apiClient → sends request with session cookie
 * 
 * SERVER SIDE (Next.js API Routes):
 * 4. Next.js API → gets session from cookie
 * 5. Next.js API → gets ID token from Redis
 * 6. Next.js API → calls Express API with Bearer token
 * 
 * EXTERNAL API (Express):
 * 7. Express API → validates Bearer token
 * 8. Express API → returns data
 * 
 * Response flows back: Express → Next.js → React → UI
 */
export function useOrganizations() {
    const [organizations, setOrganizations] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const { user, switchOrganization: switchOrgContext } = useAuth();

    useEffect(() => {
        fetchOrganizations();
    }, []);

    const fetchOrganizations = async () => {
        try {
            setLoading(true);
            setError(null);
            
            // This calls localhost:3000/api/user/organizations
            const response = await userAPI.getOrganizations();
            
            if (response.success) {
                setOrganizations(response.data || []);
            }
        } catch (err: any) {
            setError(err.message || 'Failed to fetch organizations');
        } finally {
            setLoading(false);
        }
    };

    const switchOrganization = async (organizationId: string) => {
        try {
            setLoading(true);
            
            // 1. Call our Next.js API to switch organization
            const response = await authAPI.switchOrganization(organizationId);
            
            if (response.success) {
                // 2. Update local auth context
                await switchOrgContext(organizationId);
                
                // 3. Update apiClient's organization context for future requests
                apiClient.setOrganizationId(organizationId);
                
                // 4. Refresh organizations list
                await fetchOrganizations();
                
                return true;
            }
            
            return false;
        } catch (err: any) {
            setError(err.message || 'Failed to switch organization');
            return false;
        } finally {
            setLoading(false);
        }
    };

    return {
        organizations,
        currentOrganizationId: user?.organizationId,
        loading,
        error,
        refetch: fetchOrganizations,
        switchOrganization
    };
}
