/**
 * API client for making requests to the Next.js API routes
 */
export const apiClient = async <T = unknown>(
    url: string,
    options?: Parameters<typeof fetch>[1]
): Promise<T> => {
    // Don't override Content-Type for FormData - let the browser set it automatically
    const headers: Record<string, string> = {};
    if (!(options?.body instanceof FormData)) {
        headers["Content-Type"] = "application/json";
    }

    let response;
    try {
        response = await fetch(url, {
            headers,
            ...options,
        });
    } catch (error) {
        // Handle network errors (e.g., "Failed to fetch")
        if (error instanceof Error && error.name === "TypeError") {
            throw new Error("Network error: Failed to connect to the server");
        }
        throw error;
    }

    if (!response.ok) {
        const error = await response
            .json()
            .catch(() => ({ message: "Request failed" }));
        throw new Error(
            error.message ||
                `API Error: ${response.status} ${response.statusText}`
        );
    }

    return response.json() as Promise<T>;
};
