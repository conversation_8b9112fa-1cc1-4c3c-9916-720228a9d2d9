"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  return (
    <SidebarGroup className="overflow-hidden min-w-0">
      <SidebarGroupLabel className="truncate">Platform</SidebarGroupLabel>
      <SidebarMenu className="min-w-0">
        {items.map((item) =>
          item.items ? (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={item.isActive || false}
              className="group/collapsible min-w-0"
            >
              <SidebarMenuItem className="min-w-0">
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    className="w-full min-w-0"
                  >
                    {item.icon && <item.icon className="shrink-0" />}
                    <span className="truncate flex-1">{item.title}</span>
                    <ChevronRight className="ml-auto shrink-0 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent className="min-w-0">
                  <SidebarMenuSub className="min-w-0">
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem
                        key={subItem.title}
                        className="min-w-0"
                      >
                        <SidebarMenuSubButton
                          asChild
                          className="w-full min-w-0"
                        >
                          <a
                            href={subItem.url}
                            className="flex items-center min-w-0"
                          >
                            <span className="truncate">{subItem.title}</span>
                          </a>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ) : (
            <SidebarMenuItem key={item.title} className="min-w-0">
              <SidebarMenuButton
                asChild
                tooltip={item.title}
                className="w-full min-w-0"
              >
                <a href={item.url} className="flex items-center min-w-0">
                  {item.icon && <item.icon className="shrink-0" />}
                  <span className="truncate flex-1">{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
