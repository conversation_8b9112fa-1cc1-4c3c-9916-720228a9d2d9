"use client";

import { usePermissions, usePermissionUI } from "@/hooks/use-permissions";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

/**
 * Example component demonstrating permission-based UI rendering
 */
export function PermissionExample() {
    const { hasPermission, hasRole, permissions, roles, organizationId } =
        usePermissions();
    const uiPermissions = usePermissionUI();

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <CardTitle>Current User Context</CardTitle>
                    <CardDescription>
                        Organization:{" "}
                        {organizationId || "No organization selected"}
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <h4 className="text-sm font-medium mb-2">Roles</h4>
                        <div className="flex gap-2 flex-wrap">
                            {roles.length > 0 ? (
                                roles.map((role) => (
                                    <Badge key={role} variant="secondary">
                                        {role}
                                    </Badge>
                                ))
                            ) : (
                                <span className="text-sm text-muted-foreground">
                                    No roles assigned
                                </span>
                            )}
                        </div>
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Permissions ({permissions.length})
                        </h4>
                        <div className="max-h-40 overflow-y-auto space-y-1">
                            {permissions.length > 0 ? (
                                permissions.slice(0, 10).map((permission) => (
                                    <div
                                        key={permission}
                                        className="text-xs text-muted-foreground font-mono"
                                    >
                                        {permission}
                                    </div>
                                ))
                            ) : (
                                <span className="text-sm text-muted-foreground">
                                    No permissions assigned
                                </span>
                            )}
                            {permissions.length > 10 && (
                                <div className="text-xs text-muted-foreground">
                                    ... and {permissions.length - 10} more
                                </div>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Permission-Based UI Elements</CardTitle>
                    <CardDescription>
                        These elements are shown/hidden based on user
                        permissions
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Admin Actions */}
                    {uiPermissions.isAdmin && (
                        <div className="p-4 border rounded-lg bg-destructive/10">
                            <h4 className="font-medium mb-2">Admin Actions</h4>
                            <div className="space-x-2">
                                <Button variant="destructive" size="sm">
                                    Delete Organization
                                </Button>
                                <Button variant="outline" size="sm">
                                    Manage Platform
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* User Management */}
                    {uiPermissions.canManageUsers && (
                        <div className="p-4 border rounded-lg">
                            <h4 className="font-medium mb-2">
                                User Management
                            </h4>
                            <div className="space-x-2">
                                <Button size="sm">Invite Users</Button>
                                <Button variant="outline" size="sm">
                                    Manage Roles
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* Billing */}
                    {uiPermissions.canViewBilling && (
                        <div className="p-4 border rounded-lg">
                            <h4 className="font-medium mb-2">Billing</h4>
                            <div className="space-x-2">
                                <Button size="sm">View Invoices</Button>
                                {uiPermissions.canManageBilling && (
                                    <Button variant="outline" size="sm">
                                        Update Payment Method
                                    </Button>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Project Management */}
                    {hasPermission("global|org:*|projects|view") && (
                        <div className="p-4 border rounded-lg">
                            <h4 className="font-medium mb-2">Projects</h4>
                            <div className="space-x-2">
                                <Button size="sm">View Projects</Button>
                                {uiPermissions.canManageProjects && (
                                    <Button variant="outline" size="sm">
                                        Create Project
                                    </Button>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Job Execution */}
                    {uiPermissions.canExecuteJobs && (
                        <div className="p-4 border rounded-lg">
                            <h4 className="font-medium mb-2">Job Execution</h4>
                            <div className="space-x-2">
                                <Button size="sm">Run Job</Button>
                                <Button variant="outline" size="sm">
                                    View Job History
                                </Button>
                            </div>
                        </div>
                    )}

                    {/* Custom Permissions */}
                    {hasPermission(
                        "custom|qbraid_premium|advanced_analytics|access"
                    ) && (
                        <div className="p-4 border rounded-lg bg-primary/10">
                            <h4 className="font-medium mb-2">
                                Premium Features
                            </h4>
                            <div className="space-x-2">
                                <Button size="sm" variant="default">
                                    Advanced Analytics
                                </Button>
                                <Badge>Premium User</Badge>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
