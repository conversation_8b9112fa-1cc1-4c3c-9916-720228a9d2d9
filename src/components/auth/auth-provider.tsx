"use client";

import React, {
    createContext,
    useContext,
    useEffect,
    useState,
    useCallback,
} from "react";
import { useRouter } from "next/navigation";
import { getSession, clearSession } from "@/lib/auth/session";
import { SessionPayload } from "@/types/auth";

interface AuthContextType {
    user: SessionPayload | null;
    isLoading: boolean;
    signIn: (credentials: { email: string; password: string }) => Promise<void>;
    signOut: () => Promise<void>;
    refreshUser: () => Promise<void>;
    switchOrganization: (organizationId: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const [user, setUser] = useState<SessionPayload | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();

    const refreshUser = useCallback(async () => {
        try {
            const session = await getSession();
            setUser(session);
        } catch (error) {
            console.error("Failed to refresh user session:", error);
            setUser(null);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        refreshUser();
    }, [refreshUser]);

    const signIn = async (credentials: { email: string; password: string }) => {
        setIsLoading(true);
        try {
            // Call the sign-in API directly
            const response = await fetch("/api/auth/signin", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(credentials),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Authentication failed");
            }

            if (data.success) {
                await refreshUser();
                return;
            }

            throw new Error(data.error || "Authentication failed");
        } finally {
            setIsLoading(false);
        }
    };

    const signOut = async () => {
        setIsLoading(true);
        try {
            await clearSession();
            setUser(null);
            router.push("/signin");
        } catch (error) {
            console.error("Sign out error:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const switchOrganization = async (
        organizationId: string
    ): Promise<boolean> => {
        try {
            const response = await fetch("/api/auth/switch-organization", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                credentials: "include",
                body: JSON.stringify({ organizationId }),
            });

            if (response.ok) {
                // Refresh user data to get new permissions
                await refreshUser();
                // Force a page refresh to update all components
                router.refresh();
                return true;
            }
            return false;
        } catch (error) {
            console.error("Error switching organization:", error);
            return false;
        }
    };

    const value = {
        user,
        isLoading,
        signIn,
        signOut,
        refreshUser,
        switchOrganization,
    };

    return (
        <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
    );
}

export function useAuth() {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
}
