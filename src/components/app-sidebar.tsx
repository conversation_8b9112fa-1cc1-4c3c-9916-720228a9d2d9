"use client";

import * as React from "react";
import {
  LayoutDashboard,
  Monitor,
  Zap,
  Building,
  GraduationCap,
  Compass,
  User,
  BookOpen,
  MessageCircle,
  Users,
  Command,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavSupport } from "@/components/nav-support";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import { usePermissions } from "@/hooks/use-permissions";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar";

// Define navigation items with their required permissions
const allNavItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
    isActive: true,
    permission: "global|org:*|dashboard|view",
  },
  {
    title: "Devices",
    url: "/devices",
    icon: Monitor,
    permission: "global|org:*|devices|view",
  },
  {
    title: "Jobs",
    url: "/jobs",
    icon: Zap,
    permission: "global|org:*|jobs|view",
  },
  {
    title: "Organizations",
    url: "/organizations",
    icon: Building,
    permission: "global|org:*|organizations|view",
  },
  {
    title: "Learn",
    url: "/learn",
    icon: GraduationCap,
    permission: "global|*|courses|view",
  },
  {
    title: "Explore",
    url: "/explore",
    icon: Compass,
    permission: "global|*|explore|view",
  },
  {
    title: "Account",
    url: "#",
    icon: User,
    permission: "global|user:self|profile|view",
    items: [
      {
        title: "Compute Usage",
        url: "/account/usage",
        permission: "global|org:*|usage|view",
      },
      {
        title: "Billing",
        url: "/account/billing",
        permission: "global|org:*|billing|view",
      },
      {
        title: "Profile",
        url: "/account/profile",
        permission: "global|user:self|profile|view",
      },
    ],
  },
];

const supportItems = [
  {
    title: "Documentation",
    url: "/docs",
    icon: BookOpen,
  },
  {
    title: "Feedback",
    url: "/feedback",
    icon: MessageCircle,
  },
  {
    title: "Community Support",
    url: "/community",
    icon: Users,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { hasPermission } = usePermissions();

  // Filter navigation items based on permissions
  const filteredNavItems = allNavItems.filter((item) => {
    if (!item.permission) return true; // No permission required

    // Check main item permission
    if (!hasPermission(item.permission)) return false;

    // Check sub-item permissions
    if (item.items) {
      item.items = item.items.filter(
        (subItem) => !subItem.permission || hasPermission(subItem.permission),
      );
    }

    return true;
  });

  // Mock user data - replace with actual user data from your auth system
  const userData = {
    name: "John Doe",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  };

  const teams = [
    {
      name: "qBraid",
      logo: Command,
      plan: "Pro",
    },
  ];

  return (
    <Sidebar
      variant="inset"
      collapsible="icon"
      {...props}
      className="overflow-hidden bg-muted/50"
    >
      <SidebarHeader>
        <TeamSwitcher teams={teams} />
      </SidebarHeader>
      <SidebarContent className="flex flex-col justify-between">
        <NavMain items={filteredNavItems} />
        <NavSupport items={supportItems} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
