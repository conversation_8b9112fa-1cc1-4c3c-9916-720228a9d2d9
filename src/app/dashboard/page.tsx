import { AppSidebar } from "@/components/app-sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Search } from "lucide-react";

export default function Page() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Overview</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto flex items-center gap-2 px-4">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
              <Input placeholder="Search..." className="w-64 pl-8" />
            </div>
            <ThemeToggle />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="grid auto-rows-min gap-4 md:grid-cols-3">
            <div className="flex aspect-video items-center justify-center rounded-xl bg-muted/50">
              <div className="text-center">
                <h3 className="text-lg font-semibold">Active Jobs</h3>
                <p className="text-2xl font-bold text-brand">24</p>
              </div>
            </div>
            <div className="flex aspect-video items-center justify-center rounded-xl bg-muted/50">
              <div className="text-center">
                <h3 className="text-lg font-semibold">Devices</h3>
                <p className="text-2xl font-bold text-brand">3</p>
              </div>
            </div>
            <div className="flex aspect-video items-center justify-center rounded-xl bg-muted/50">
              <div className="text-center">
                <h3 className="text-lg font-semibold">Usage This Month</h3>
                <p className="text-2xl font-bold text-brand">84%</p>
              </div>
            </div>
          </div>
          <div className="flex min-h-screen flex-1 items-center justify-center rounded-xl bg-muted/50 md:min-h-min">
            <div className="text-center">
              <h2 className="text-2xl font-bold">
                Welcome to qBraid Dashboard
              </h2>
              <p className="text-muted-foreground mt-2">
                Manage your quantum computing resources and jobs
              </p>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
