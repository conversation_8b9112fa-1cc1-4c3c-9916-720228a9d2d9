@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #171717;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #171717;
  --brand: #8b5cf6;
  --brand-foreground: #ffffff;
  --chart-1: #ef4444;
  --chart-2: #22c55e;
  --chart-3: #3b82f6;
  --chart-4: #f59e0b;
  --chart-5: #8b5cf6;
  --radius: 0.5rem;
  --sidebar-background: #fafafa;
  --sidebar-foreground: #171717;
  --sidebar-primary: #171717;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #171717;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-brand: var(--brand);
  --color-brand-foreground: var(--brand-foreground);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar-background: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@variant dark (&:where(.dark, .dark *));

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #0a0a0a;
  --card-foreground: #ededed;
  --popover: #0a0a0a;
  --popover-foreground: #ededed;
  --primary: #ededed;
  --primary-foreground: #0a0a0a;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ededed;
  --border: #262626;
  --input: #262626;
  --ring: #ededed;
  --brand: #a855f7;
  --brand-foreground: #ededed;
  --chart-1: #dc2626;
  --chart-2: #16a34a;
  --chart-3: #2563eb;
  --chart-4: #d97706;
  --chart-5: #a855f7;
  --sidebar-background: #111111;
  --sidebar-foreground: #ededed;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ededed;
  --sidebar-accent: #262626;
  --sidebar-accent-foreground: #ededed;
  --sidebar-border: #262626;
  --sidebar-ring: #3b82f6;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --card: #0a0a0a;
    --card-foreground: #ededed;
    --popover: #0a0a0a;
    --popover-foreground: #ededed;
    --primary: #ededed;
    --primary-foreground: #0a0a0a;
    --secondary: #262626;
    --secondary-foreground: #ededed;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --accent: #262626;
    --accent-foreground: #ededed;
    --destructive: #dc2626;
    --destructive-foreground: #ededed;
    --border: #262626;
    --input: #262626;
    --ring: #ededed;
    --brand: #a855f7;
    --brand-foreground: #ededed;
    --chart-1: #dc2626;
    --chart-2: #16a34a;
    --chart-3: #2563eb;
    --chart-4: #d97706;
    --chart-5: #a855f7;
    --sidebar-background: #111111;
    --sidebar-foreground: #ededed;
    --sidebar-primary: #3b82f6;
    --sidebar-primary-foreground: #ededed;
    --sidebar-accent: #262626;
    --sidebar-accent-foreground: #ededed;
    --sidebar-border: #262626;
    --sidebar-ring: #3b82f6;
  }
}

* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
}

.transition-all {
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  /* Hide scrollbar utility */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  .animate-in {
    animation-duration: 0.3s;
    animation-fill-mode: both;
  }

  .fade-in {
    animation-name: fade-in;
  }

  .slide-in-from-top-2 {
    animation-name: slide-in-from-top-2;
  }

  .slide-in-from-right-2 {
    animation-name: slide-in-from-right-2;
  }

  .slide-in-from-bottom-2 {
    animation-name: slide-in-from-bottom-2;
  }

  .slide-in-from-left {
    animation-name: slide-in-from-left;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slide-in-from-top-2 {
    from {
      transform: translateY(-0.5rem);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-right-2 {
    from {
      transform: translateX(0.5rem);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-bottom-2 {
    from {
      transform: translateY(0.5rem);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-from-left {
    from {
      transform: translateX(-1rem);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Modal animations */
  .modal-fade-in {
    animation: modalFadeIn 0.3s ease-out;
  }

  .modal-scale-in {
    animation: modalScaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  @keyframes modalFadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }

  @keyframes modalScaleIn {
    from {
      transform: scale(0.95) translateY(10px);
      opacity: 0;
    }
    to {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
  }

  /* Smooth hover transitions */
  .hover-lift {
    transition: all 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px -5px rgba(139, 92, 246, 0.25);
  }

  /* Gradient text animation */
  .gradient-text {
    background: linear-gradient(to right, #8b5cf6, #a855f7, #8b5cf6);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Focus glow effect */
  .focus-glow {
    transition: all 0.3s ease;
  }

  .focus-glow:focus {
    box-shadow:
      0 0 0 3px rgba(139, 92, 246, 0.1),
      0 0 20px -5px rgba(139, 92, 246, 0.5);
  }

  /* Tab indicator animation */
  .tab-indicator {
    position: relative;
    overflow: hidden;
  }

  .tab-indicator::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, #8b5cf6, #a855f7);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
  }

  .tab-indicator[data-state="active"]::after {
    transform: scaleX(1);
  }

  /* Shimmer effect for loading states */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.05),
      transparent
    );
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }

  /* Pulse animation for icons */
  .pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulseSoft {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* Stagger animation for list items */
  .stagger-fade-in {
    opacity: 0;
    animation: staggerFadeIn 0.5s ease forwards;
  }

  .stagger-fade-in:nth-child(1) {
    animation-delay: 0ms;
  }
  .stagger-fade-in:nth-child(2) {
    animation-delay: 50ms;
  }
  .stagger-fade-in:nth-child(3) {
    animation-delay: 100ms;
  }
  .stagger-fade-in:nth-child(4) {
    animation-delay: 150ms;
  }

  @keyframes staggerFadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Blob animation for auth pages */
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
}

@layer utilities {
  /* Enhanced scrollbar for modal content */
  .modal-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
  }

  .modal-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .modal-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(139, 92, 246, 0.3);
    border-radius: 3px;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 92, 246, 0.5);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(10, 10, 15, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Neon glow for active elements */
  .neon-glow {
    box-shadow:
      0 0 20px rgba(139, 92, 246, 0.5),
      0 0 40px rgba(139, 92, 246, 0.3),
      0 0 60px rgba(139, 92, 246, 0.1);
  }

  /* Enhanced font rendering for better readability */
  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Improved text contrast classes */
  .text-enhanced {
    color: #f1f5f9;
    font-weight: 500;
  }

  .text-enhanced-muted {
    color: #cbd5e1;
    font-weight: 500;
  }

  .text-enhanced-strong {
    color: #ffffff;
    font-weight: 600;
  }

  /* Better button text visibility */
  .btn-enhanced {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  /* Custom scrollbar styles */
  .always-show-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #374151 #1f2937;
  }

  .always-show-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .always-show-scrollbar::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 4px;
  }

  .always-show-scrollbar::-webkit-scrollbar-thumb {
    background: #374151;
    border-radius: 4px;
  }

  .always-show-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #4b5563;
  }
}

@layer base {
  * {
    border-color: var(--border);
  }

  body {
    overscroll-behavior-y: none;
  }
}

@layer utilities {
  .animate-dropdown {
    animation: dropdown-fade-in 0.1s ease;
  }
  /* Disable all on-load animate-in and slide/fade effects */
  .animate-in,
  .fade-in,
  .slide-in-from-bottom,
  .slide-in-from-left,
  .slide-in-from-right,
  .slide-in-from-top {
    animation: none !important;
  }
  @keyframes dropdown-fade-in {
    0% {
      opacity: 0;
      transform: scaleY(0.95) translateY(-8px);
    }
    100% {
      opacity: 1;
      transform: scaleY(1) translateY(0);
    }
  }
}

@layer utilities {
  .always-show-scrollbar {
    scrollbar-width: auto; /* For Firefox */
    scrollbar-color: #888 #1a1a1a;

    /* For WebKit browsers */
  }

  .always-show-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .always-show-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
  }

  .always-show-scrollbar::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
    border: 2px solid #1a1a1a;
  }
}

/* Custom animations */
@keyframes bounce-slow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s infinite;
}

/* Enhanced sidebar collapse animations */
@keyframes sidebar-collapse-width {
  from {
    width: var(--sidebar-width);
  }
  to {
    width: var(--sidebar-width-icon);
  }
}

@keyframes sidebar-expand-width {
  from {
    width: var(--sidebar-width-icon);
  }
  to {
    width: var(--sidebar-width);
  }
}

/* Smooth sidebar container transitions */
[data-slot="sidebar-container"] {
  transition:
    width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-slot="sidebar-gap"] {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced sidebar trigger button */
[data-sidebar="trigger"] {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, background-color;
}

[data-sidebar="trigger"]:hover {
  background-color: rgba(139, 92, 246, 0.1);
  transform: scale(1.05);
}

/* Sidebar content smooth transitions */
[data-sidebar="sidebar"] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Menu button enhanced transitions */
[data-sidebar="menu-button"] {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, padding;
}

[data-sidebar="menu-button"]:hover:not([data-collapsible="icon"]) {
  transform: translateX(2px);
}

/* Fix sidebar gap element for collapsed state */
[data-variant="inset"][data-collapsible="icon"] [data-slot="sidebar-gap"] {
  width: calc(var(--sidebar-width-icon) + 0.5rem) !important;
}
