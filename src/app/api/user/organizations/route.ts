import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/session";
import { organizationAPI } from "@/lib/api/organization-api";

/**
 * Example showing the flow:
 * 1. UI calls localhost:3000/api/user/organizations (Next.js backend)
 * 2. Next.js backend gets session, extracts token
 * 3. Next.js backend calls localhost:3001/api/v1/... (Express API) with Bearer token
 * 4. Express API validates token and returns data
 * 5. Next.js backend returns data to UI
 */
export async function GET(request: NextRequest) {
    try {
        // 1. Get session from cookie (UI → Next.js)
        const session = await getSession();
        
        if (!session?.signedIn) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // 2. Next.js backend automatically includes Bearer token when calling Express API
        // The externalClient handles this via interceptors
        const organizations = await organizationAPI.listUserOrganizations(session.email);

        // 3. Return data to UI
        return NextResponse.json({
            success: true,
            data: organizations,
            meta: {
                currentOrganizationId: session.organizationId,
                email: session.email
            }
        });
    } catch (error: any) {
        console.error("[API] Organizations route error:", error);
        
        return NextResponse.json(
            { error: error.message || "Failed to fetch organizations" },
            { status: error.status || 500 }
        );
    }
}
