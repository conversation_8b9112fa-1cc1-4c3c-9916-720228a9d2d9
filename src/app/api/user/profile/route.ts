import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/session";
import { userAPI } from "@/lib/api/user-api";
import { getUserContextByEmail } from "@/lib/auth/session";

/**
 * GET /api/user/profile
 * Returns the authenticated user's profile using the external API client
 */
export async function GET(request: NextRequest) {
    try {
        // 1. Get session (external client will automatically use the token)
        const session = await getSession();
        
        if (!session?.signedIn) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );
        }

        // 2. Check if we have cached user context
        const cachedContext = await getUserContextByEmail(session.email);
        if (cachedContext) {
            console.log("[API] Using cached user context from Redis");
            return NextResponse.json({
                success: true,
                source: "cache",
                data: cachedContext,
            });
        }

        // 3. Fetch fresh data from external API
        // The external client automatically includes the Bearer token
        const profileData = await userAPI.getProfile(session.email);

        return NextResponse.json({
            success: true,
            source: "external-api",
            data: profileData,
            session: {
                sessionId: session.jti,
                email: session.email,
                organizationId: session.organizationId,
                permissions: session.permissions?.length || 0,
                roles: session.roles || [],
            },
        });
    } catch (error: any) {
        console.error("[API] Profile route error:", error);
        
        // Handle external API errors
        if (error.status === 401) {
            return NextResponse.json(
                { error: "Authentication expired" },
                { status: 401 }
            );
        }
        
        return NextResponse.json(
            { error: error.message || "Internal server error" },
            { status: error.status || 500 }
        );
    }
}
