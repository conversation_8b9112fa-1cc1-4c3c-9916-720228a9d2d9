import { NextRequest, NextResponse } from "next/server";
import { authRateLimiters, applyRateLimit } from "@/lib/utils/rate-limiter";
import { authLogger } from "@/lib/auth/auth-logger";
import { UserService } from "@/lib/auth/user-service";
import {
    updateSessionPermissions,
    getCognitoTokenCookies,
    getSession,
} from "@/lib/auth/session";
import { getSessionContext } from "@/lib/auth/session-middleware";
import { z } from "zod";

const switchOrgSchema = z.object({
    organizationId: z.string().min(1, "Organization ID is required"),
});

export async function POST(request: NextRequest) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(
        request,
        authRateLimiters.api
    );
    if (rateLimitResponse) {
        return rateLimitResponse;
    }

    try {
        const body = await request.json();

        // Validate input
        const validationResult = switchOrgSchema.safeParse(body);
        if (!validationResult.success) {
            return NextResponse.json(
                {
                    error:
                        validationResult.error.issues?.[0]?.message ||
                        "Invalid input",
                },
                { status: 400 }
            );
        }

        const { organizationId } = validationResult.data;

        // Get session context with tokens
        const sessionContext = await getSessionContext(request);
        if (!sessionContext) {
            return NextResponse.json(
                { error: "No valid session" },
                { status: 401 }
            );
        }

        const { sessionId, userId, email: userEmail, idToken } = sessionContext;

        const userProfileResult = await UserService.getUserProfile(
            userId,
            idToken,
            organizationId,
            userEmail || undefined
        );

        if (!userProfileResult.success || !userProfileResult.user) {
            await authLogger.logEvent("ORGANIZATION_SWITCH_FAILED", {
                userId,
                success: false,
                error: "Failed to fetch user profile for organization",
                request,
                metadata: { organizationId },
            });
            return NextResponse.json(
                { error: "Failed to load user profile for organization" },
                { status: 500 }
            );
        }

        const userProfile = userProfileResult.user;

        // Update session with new organization permissions
        const updated = await updateSessionPermissions(
            sessionId,
            userProfile.permissions,
            userProfile.roles,
            organizationId
        );

        if (!updated) {
            await authLogger.logEvent("ORGANIZATION_SWITCH_FAILED", {
                userId,
                success: false,
                error: "Failed to update session permissions",
                request,
                metadata: { organizationId },
            });
            return NextResponse.json(
                { error: "Failed to update session" },
                { status: 500 }
            );
        }

        // Log successful organization switch
        await authLogger.logEvent("ORGANIZATION_SWITCH_SUCCESS", {
            userId,
            success: true,
            request,
            metadata: {
                organizationId,
                permissionsCount: userProfile.permissions.length,
                rolesCount: userProfile.roles.length,
            },
        });

        return NextResponse.json({
            success: true,
            organizationId,
            permissions: userProfile.permissions,
            roles: userProfile.roles,
        });
    } catch (error) {
        await authLogger.logEvent("ORGANIZATION_SWITCH_ERROR", {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            request,
        });

        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
