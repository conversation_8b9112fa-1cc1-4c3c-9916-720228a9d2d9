import { NextRequest, NextResponse } from "next/server";
import { authRateLimiters, applyRateLimit } from "@/lib/utils/rate-limiter";
import { authLogger } from "@/lib/auth/auth-logger";
import { CognitoService } from "@/lib/auth/cognito-service";
import { createSession, setSessionCookie } from "@/lib/auth/session";
import { UserService } from "@/lib/auth/user-service";
import { jwtVerify, createRemoteJWKSet } from "jose";
import { z } from "zod";

// Schema for direct Cognito authentication
const directSignInSchema = z.object({
    email: z
        .string()
        .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, "Invalid email format"),
    password: z.string().min(1, "Password is required"),
});

// Schema for token-based authentication (when Cognito auth is done on frontend)
const tokenSignInSchema = z.object({
    idToken: z.string().min(1, "ID token is required"),
    accessToken: z.string().min(1, "Access token is required"),
    refreshToken: z.string().optional(),
});

export async function POST(request: NextRequest) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(
        request,
        authRateLimiters.auth
    );
    if (rateLimitResponse) {
        return rateLimitResponse;
    }

    try {
        const body = await request.json();

        let authResult: Awaited<
            ReturnType<typeof CognitoService.authenticateUser>
        >;
        let userProfile: Awaited<
            ReturnType<typeof UserService.getUserProfile>
        >["user"];
        let userProfileResult: Awaited<
            ReturnType<typeof UserService.getUserProfile>
        >;
        let email: string;

        if (body.idToken && body.accessToken) {
            // Token-based authentication (Cognito auth done on frontend)
            const tokenValidation = tokenSignInSchema.safeParse(body);
            if (!tokenValidation.success) {
                return NextResponse.json(
                    { error: "Invalid token format" },
                    { status: 400 }
                );
            }

            const { idToken, accessToken, refreshToken } = tokenValidation.data;

            // Verify ID token with Cognito JWKS before trusting claims
            try {
                const region = process.env.AWS_REGION || "us-east-1";
                const userPoolId =
                    process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID;
                const clientId =
                    process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID;
                if (!userPoolId || !clientId) {
                    return NextResponse.json(
                        { error: "Server misconfiguration" },
                        { status: 500 }
                    );
                }

                const issuer = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`;
                const jwks = createRemoteJWKSet(
                    new URL(`${issuer}/.well-known/jwks.json`)
                );
                const { payload } = await jwtVerify(idToken, jwks, {
                    issuer,
                    audience: clientId,
                });

                const userId =
                    (payload.sub as string) || (payload.username as string);
                email = (payload.email as string) || "";

                authResult = {
                    success: true,
                    user: {
                        userId,
                        email,
                        username:
                            (payload["cognito:username"] as string) || email,
                    },
                    tokens: { accessToken, idToken, refreshToken },
                };

                let organizationId = undefined;
                userProfileResult = await UserService.getUserProfile(
                    userId,
                    idToken,
                    organizationId,
                    email
                );

                if (!userProfileResult.success || !userProfileResult.user) {
                    return NextResponse.json(
                        {
                            error: "Token verification failed or profile unavailable",
                        },
                        { status: 401 }
                    );
                }
                userProfile = userProfileResult.user;
            } catch (error) {
                return NextResponse.json(
                    { error: "Invalid token" },
                    { status: 400 }
                );
            }
        } else {
            // Direct Cognito authentication
            const validationResult = directSignInSchema.safeParse(body);
            if (!validationResult.success) {
                return NextResponse.json(
                    {
                        error:
                            validationResult.error.issues?.[0]?.message ||
                            "Invalid input",
                    },
                    { status: 400 }
                );
            }

            const { email: userEmail, password } = validationResult.data;
            email = userEmail;

            await authLogger.logSignInAttempt(email, false, request);

            authResult = await CognitoService.authenticateUser(email, password);

            if (!authResult.success) {
                await authLogger.logSignInFailure(
                    email,
                    authResult.error || "Authentication failed",
                    request
                );
                return NextResponse.json(
                    {
                        error: authResult.error || "Authentication failed",
                        requiresVerification: authResult.requiresVerification,
                    },
                    { status: 401 }
                );
            }

            if (!authResult.user) {
                await authLogger.logSignInFailure(
                    email,
                    "User data not available",
                    request
                );
                return NextResponse.json(
                    { error: "Authentication failed" },
                    { status: 401 }
                );
            }

            // Try to get user profile, fallback to mock data if external API is not available
            userProfileResult = await UserService.getUserProfile(
                authResult.user.userId,
                authResult.tokens?.idToken,
                undefined,
                authResult.user.email
            );

            if (!userProfileResult.success || !userProfileResult.user) {
                // Fallback to mock user profile for development
                console.warn(
                    "External API not available, using mock user profile"
                );
                userProfile = {
                    id: authResult.user.userId,
                    email: authResult.user.email,
                    name: authResult.user.name,
                    username: authResult.user.username,
                    permissions: [
                        "global|org:*|users|read",
                        "org:*|projects|read",
                        "user:self|profile|read",
                    ],
                    roles: ["org_member"],
                    currentOrganizationId: undefined,
                };
            } else {
                userProfile = userProfileResult.user;
            }
        }

        // Create session with permissions and roles
        if (!authResult.user) {
            return NextResponse.json(
                { error: "Authentication failed - no user data" },
                { status: 500 }
            );
        }

        // Create session with tokens and user context
        const sessionToken = await createSession(
            {
                userId: authResult.user.userId,
                email: authResult.user.email,
                username: authResult.user.username,
                permissions: userProfile?.permissions || [],
                roles: userProfile?.roles || [],
                organizationId: userProfile?.currentOrganizationId,
            },
            authResult.tokens
                ? {
                      idToken: authResult.tokens.idToken,
                      accessToken: authResult.tokens.accessToken,
                  }
                : undefined,
            userProfileResult.success ? userProfileResult : undefined
        );

        // Set session cookie
        await setSessionCookie(sessionToken);

        // Log successful sign-in
        await authLogger.logSignInSuccess(
            authResult.user.userId,
            email,
            undefined, // session data will be added later if needed
            request
        );

        return NextResponse.json({
            success: true,
            user: {
                id: authResult.user.userId,
                email: authResult.user.email,
                name: authResult.user.name || authResult.user.username,
                permissions: userProfile?.permissions || [],
                roles: userProfile?.roles || [],
                organizationId: userProfile?.currentOrganizationId,
            },
        });
    } catch (error) {
        await authLogger.logSignInFailure(
            "unknown",
            error instanceof Error ? error.message : "Unknown error",
            request
        );

        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
