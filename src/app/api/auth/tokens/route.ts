import { NextRequest, NextResponse } from "next/server";
import { authRateLimiters, applyRateLimit } from "@/lib/utils/rate-limiter";
import {
    getSession,
    getCognitoTokenCookies,
    verifyCSRFToken,
} from "@/lib/auth/session";
import { authLogger } from "@/lib/auth/auth-logger";

export async function POST(request: NextRequest) {
    const rateLimitResponse = await applyRateLimit(
        request,
        authRateLimiters.api
    );
    if (rateLimitResponse) return rateLimitResponse;

    try {
        const body = await request.json().catch(() => ({}));
        const csrfToken =
            body?.csrfToken || request.headers.get("x-csrf-token");
        const csrfOk = await verifyCSRFToken(csrfToken || "");
        if (!csrfOk) {
            return NextResponse.json(
                { error: "Invalid CSRF token" },
                { status: 403 }
            );
        }
        const session = await getSession();
        if (!session?.signedIn)
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 }
            );

        const sessionId = session.jti; // Redis key suffix when Redis mode is active
        const tokens = await getCognitoTokenCookies(sessionId);
        if (!tokens.accessToken && !tokens.idToken) {
            return NextResponse.json(
                { error: "Tokens not found" },
                { status: 404 }
            );
        }

        await authLogger.logEvent("TOKEN_EXCHANGE_SUCCESS", {
            userId: session.userId,
            success: true,
            request,
        });
        // Return only idToken to minimize exposure
        return NextResponse.json({ success: true, idToken: tokens.idToken });
    } catch (error) {
        await authLogger.logEvent("TOKEN_EXCHANGE_ERROR", {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            request,
        });
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}
