import { VerifyForm } from "@/components/auth/verify-form";
import { requireNoAuth } from "@/lib/auth/auth";
import { Suspense } from "react";

function VerifyPageContent() {
  return <VerifyForm />;
}

export const dynamic = "force-dynamic";

export default async function VerifyPage() {
  await requireNoAuth();

  return (
    <Suspense
      fallback={
        <div className="container flex h-screen w-screen flex-col items-center justify-center">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="text-center">Loading...</div>
          </div>
        </div>
      }
    >
      <VerifyPageContent />
    </Suspense>
  );
}
