import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import OAuthProcessing from "@/app/(auth)/oauth/oauth-processing";
import Link from "next/link";
import { apiClient } from "@/hooks/use-api";

// Server Component - handles initial OAuth callback
export default async function OAuthCallbackPage({
    searchParams,
}: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
    // Await searchParams as required in Next.js 15
    const params = await searchParams;
    // Authorization Code Flow: Check for code parameter
    const code = params.code as string | undefined;
    const state = params.state as string | undefined;

    // Error handling for OAuth errors
    if (params.error) {
        // OAuth callback error occurred
        return <OAuthError />;
    }

    // Authorization Code Flow - exchange code for tokens server-side
    if (code) {
        try {
            // This would typically call your OAuth provider's token endpoint
            // For now, we'll redirect to a server action that handles the exchange
            const cookieHeader = await cookies();
            const result = (await apiClient(
                `${process.env.NEXT_PUBLIC_APP_URL}/api/oauth/exchange`,
                {
                    method: "POST",
                    headers: {
                        // Forward cookies for CSRF protection
                        cookie: cookieHeader.toString(),
                    },
                    body: JSON.stringify({ code, state }),
                }
            )) as { redirectTo?: string };

            if (result?.redirectTo) {
                redirect(result.redirectTo);
            }
        } catch {
            // OAuth code exchange failed
            return <OAuthError />;
        }
    }

    // For both Implicit Flow (tokens in hash) and when no params are present
    // Always show the processing component - it will handle all cases
    return <OAuthProcessing />;
}

// Reusable error component
function OAuthError() {
    return (
        <div className="w-full">
            <div className="rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] p-10 shadow-2xl backdrop-blur-sm lg:p-12">
                <div className="space-y-6 text-center">
                    {/* Error icon */}
                    <div className="mb-8">
                        <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-2xl bg-red-500/20">
                            <svg
                                className="size-10 text-red-400"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                />
                            </svg>
                        </div>
                        <h1 className="text-2xl font-bold text-white">
                            Authentication Failed
                        </h1>
                    </div>

                    {/* Error message */}
                    <div className="space-y-4">
                        <p className="text-sm text-slate-300">
                            We couldn&apos;t complete the sign-in process.
                            Please try again.
                        </p>

                        <p className="text-sm text-slate-400">
                            If this problem persists, please contact support.
                        </p>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col justify-center gap-3 pt-4 sm:flex-row">
                        <Link
                            href="/signin"
                            className="rounded-xl bg-purple-600 px-6 py-3 text-center font-medium text-white shadow-lg transition-all duration-200 hover:bg-purple-700 hover:shadow-purple-600/25"
                        >
                            Try Again
                        </Link>
                        <Link
                            href="/"
                            className="rounded-xl bg-slate-700/50 px-6 py-3 text-center font-medium text-white transition-all duration-200 hover:bg-slate-700/70"
                        >
                            Go Home
                        </Link>
                    </div>

                    {/* Help text */}
                    <p className="pt-4 text-xs text-slate-500">
                        Need help? Contact <NAME_EMAIL>
                    </p>
                </div>
            </div>
        </div>
    );
}
