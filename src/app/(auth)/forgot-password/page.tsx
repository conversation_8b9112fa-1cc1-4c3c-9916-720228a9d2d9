import { Suspense } from 'react';
import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';

function ForgotPasswordFormWithSuspense() {
  return (
    <Suspense
      fallback={
        <div className="h-96 w-full max-w-md animate-pulse rounded-3xl border border-purple-800/20 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] shadow-2xl backdrop-blur-sm"></div>
      }
    >
      <ForgotPasswordForm />
    </Suspense>
  );
}

export default function ForgotPasswordPage() {
  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-md">
        <ForgotPasswordFormWithSuspense />
      </div>
    </div>
  );
}
