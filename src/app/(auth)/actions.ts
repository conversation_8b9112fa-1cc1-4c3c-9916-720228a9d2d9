// app/auth/actions.ts
"use server";

import { authLogger } from "@/lib/auth/auth-logger";
import { redirect } from "next/navigation";
import {
  createSession,
  setSessionCookie,
  clearSession,
  generateCSRFToken,
  verifyCSRFToken,
  getSession as getSecureSession,
  cleanupOrphanedSessions,
  verifySession,
} from "@/lib/auth/session";
import { invalidateUserRoles } from "@/lib/permissions/external-roles";
import { AuthResult, AuthFormState } from "@/types/auth";

// Error mapping for consistent responses
const ERROR_MESSAGES = {
  INVALID_CREDENTIALS: "Invalid email or password",
  USER_NOT_FOUND: "Invalid email or password",
  TOO_MANY_REQUESTS: "Too many attempts. Please try again later.",
  VALIDATION_ERROR: "Please check your input and try again",
} as const;

/**
 * CSRF validation
 */
const validateCSRFToken = (formData: FormData): Promise<boolean> => {
  const csrfToken = formData.get("csrfToken") as string;
  return csrfToken ? verifyCSRFToken(csrfToken) : Promise.resolve(false);
};

/**
 * Centralized error handling with consistent messaging
 */
const handleAuthError = (error: unknown, operation: string): AuthResult => {
  const message =
    error instanceof Error
      ? error.message
      : "Authentication failed. Please try again.";

  console.error(`❌ [${operation}] Error:`, {
    errorMessage: error instanceof Error ? error.message : String(error),
    timestamp: new Date().toISOString(),
  });

  return { success: false, error: message };
};

/**
 * Create authentication session
 */
const createAuthSession = async (userData: {
  username: string;
  email: string;
  userId: string;
}): Promise<string> => {
  const { username, email, userId } = userData;

  // Create session
  const sessionToken = await createSession({ username, email, userId });

  // Set session cookie
  await setSessionCookie(sessionToken);

  // Background cleanup (non-blocking)
  cleanupOrphanedSessions(email, sessionToken).catch((error) =>
    console.warn("⚠️ Session cleanup failed (non-critical):", error),
  );

  return sessionToken;
};

/**
 * Input validation helper
 */
const validateRequired = (
  fields: Record<string, string | undefined>,
): string | undefined => {
  for (const [name, value] of Object.entries(fields)) {
    if (!value?.trim()) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} is required`;
    }
  }
  return undefined;
};

/**
 * Password validation helper
 */
const validatePassword = (
  password: string,
  confirmPassword?: string,
): string | undefined => {
  if (password.length < 8) {
    return "Password must be at least 8 characters long";
  }

  if (confirmPassword && password !== confirmPassword) {
    return "Passwords do not match";
  }

  return undefined;
};

/**
 * User authentication using custom API
 */
export async function authenticateUser(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  try {
    // Log sign-in attempt
    await authLogger.logSignInAttempt(email, false, undefined, undefined, {
      source: "server-action",
    });

    // CSRF validation
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      await authLogger.logSignInFailure(
        email,
        "CSRF token validation failed",
        undefined,
        {
          source: "server-action",
        },
      );
      return {
        success: false,
        error: "Invalid request. Please try again.",
      };
    }

    // Input validation
    const validationError = validateRequired({ email, password });
    if (validationError) {
      await authLogger.logSignInFailure(email, validationError, undefined, {
        source: "server-action",
        validationError: validationError,
      });
      return { success: false, error: validationError };
    }

    // Call the sign-in API
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/api/auth/signin`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      },
    );

    const data = await response.json();

    if (!response.ok) {
      await authLogger.logSignInFailure(
        email,
        data.error || "Authentication failed",
        undefined,
        {
          source: "server-action",
          statusCode: response.status,
        },
      );
      return {
        success: false,
        error: data.error || "Authentication failed",
      };
    }

    if (data.success) {
      // Get session data for logging
      const sessionData = await getSecureSession();
      if (sessionData) {
        await authLogger.logSignInSuccess(
          data.user.id,
          email,
          sessionData,
          undefined,
          {
            source: "server-action",
          },
        );
      }

      return { success: true, redirectTo: "/" };
    }

    return { success: false, error: "Authentication failed" };
  } catch (error) {
    const errorResult = handleAuthError(error, "AUTH");
    await authLogger.logSignInFailure(
      email,
      errorResult.error || "Unknown error",
      undefined,
      {
        source: "server-action",
        errorName: error instanceof Error ? error.name : "Unknown",
      },
    );
    return errorResult;
  }
}

/**
 * User registration (simplified for now)
 */
export async function registerUser(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  try {
    // Log sign-up attempt
    await authLogger.logSignUpAttempt(email, false);

    // CSRF validation
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return {
        success: false,
        error: "Invalid request. Please try again.",
      };
    }

    // Input validation
    const requiredError = validateRequired({ name, email, password });
    if (requiredError) return { success: false, error: requiredError };

    const passwordError = validatePassword(password, confirmPassword);
    if (passwordError) return { success: false, error: passwordError };

    // For now, just return success (in production, you'd create the user)
    await authLogger.logSignUpAttempt(email, true);
    return {
      success: true,
      error: "Registration not yet implemented. Please contact support.",
    };
  } catch (error) {
    const errorResult = handleAuthError(error, "REGISTER");
    await authLogger.logSignUpAttempt(
      email,
      false,
      undefined,
      errorResult.error,
    );
    return errorResult;
  }
}

/**
 * Email verification (simplified)
 */
export async function verifyEmail(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return {
        success: false,
        error: "Invalid request. Please try again.",
      };
    }

    const email = formData.get("email") as string;
    const code = formData.get("code") as string;

    const validationError = validateRequired({ email, code });
    if (validationError) return { success: false, error: validationError };

    // For now, just return success (in production, you'd verify the code)
    return {
      success: true,
      error: "Email verification not yet implemented.",
    };
  } catch (error) {
    return handleAuthError(error, "VERIFY");
  }
}

/**
 * Password reset initiation (simplified)
 */
export async function initiatePasswordReset(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return {
        success: false,
        error: "Invalid request. Please try again.",
      };
    }

    const email = formData.get("email") as string;
    if (!email) return { success: false, error: "Email is required" };

    // For now, just return success (in production, you'd send reset email)
    return {
      success: true,
      nextStep: "reset",
      error: "Password reset not yet implemented. Please contact support.",
    };
  } catch (error) {
    return handleAuthError(error, "RESET");
  }
}

/**
 * Password reset completion (simplified)
 */
export async function completePasswordReset(
  prevState: AuthFormState | undefined,
  formData: FormData,
): Promise<AuthResult> {
  try {
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return {
        success: false,
        error: "Invalid request. Please try again.",
      };
    }

    const email = formData.get("email") as string;
    const code = formData.get("code") as string;
    const newPassword = formData.get("newPassword") as string;
    const confirmPassword = formData.get("confirmPassword") as string;

    const requiredError = validateRequired({ email, code, newPassword });
    if (requiredError) return { success: false, error: requiredError };

    const passwordError = validatePassword(newPassword, confirmPassword);
    if (passwordError) return { success: false, error: passwordError };

    // For now, just return success (in production, you'd reset the password)
    return { success: true, error: "Password reset not yet implemented." };
  } catch (error) {
    return handleAuthError(error, "RESET_COMPLETE");
  }
}

/**
 * Logout function
 */
export async function logout() {
  let currentSessionId: string | undefined;
  let userEmail: string | undefined;

  try {
    // Get session details
    const sessionDetails = await getSecureSession();
    currentSessionId = sessionDetails?.jti || undefined;
    userEmail = sessionDetails?.email || undefined;

    // Log logout attempt
    if (userEmail && sessionDetails) {
      await authLogger.logSignOut(
        sessionDetails?.userId || userEmail,
        userEmail,
        sessionDetails,
      );
    }
  } catch (error) {
    console.error("❌ [LOGOUT] Error:", error);
    if (userEmail) {
      // Create a minimal session object for logging
      const now = Math.floor(Date.now() / 1000);
      const minimalSession = {
        userId: userEmail,
        email: userEmail,
        username: userEmail,
        signedIn: false,
        jti: currentSessionId || "unknown",
        iat: now,
        exp: now + 3600, // 1 hour from now
      };
      await authLogger.logSignOut(userEmail, userEmail, minimalSession);
    }
  } finally {
    // Parallel cleanup operations
    const cleanupTasks = [clearSession(currentSessionId || undefined)];

    if (userEmail) {
      cleanupTasks.push(
        invalidateUserRoles(userEmail).catch((error) =>
          console.error("Failed to invalidate user roles:", error),
        ),
      );
    }

    await Promise.all(cleanupTasks);
    redirect("/signin");
  }
}

/**
 * Session retrieval (unchanged)
 */
export async function getSession() {
  return await getSecureSession();
}

/**
 * Session validation with automatic logout
 */
export async function validateSessionOrLogout() {
  const session = await getSecureSession();

  if (!session) {
    // Non-blocking logout
    logout().catch((error) => console.error("Failed to force logout:", error));
    return;
  }

  return session;
}

/**
 * CSRF token generation
 */
export async function getCSRFToken(): Promise<string> {
  return await generateCSRFToken();
}

/**
 * Google OAuth sign-in (simplified)
 */
export async function signInWithGoogle(): Promise<void> {
  // For now, redirect to a placeholder
  redirect("/signin?error=oauth-not-implemented");
}

/**
 * OAuth callback handling (simplified)
 */
export async function handleOAuthCallback(): Promise<AuthResult> {
  return { success: false, error: "OAuth not yet implemented" };
}

/**
 * OAuth session creation (simplified)
 */
export async function createOAuthSession(userData: {
  userId: string;
  email: string;
  username?: string;
  tokens?: {
    accessToken: string;
    idToken: string;
  };
}): Promise<AuthResult> {
  return { success: false, error: "OAuth not yet implemented" };
}

/**
 * Finalize OAuth (simplified)
 */
export async function finalizeOAuth(
  formData: FormData,
): Promise<{ ok: boolean }> {
  return { ok: false };
}
