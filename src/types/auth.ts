// Authentication and session related types

/**
 * Standard return type for all authentication operations
 */
export interface AuthResult {
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  alreadyAuthenticated?: boolean;
  nextStep?: string;
  redirectTo?: string;
  email?: string;
  csrfToken?: string;
}

// Permission system types based on AuthZ guide

/**
 * Permission structure: scope|qrn|resource|action
 */
export interface Permission {
  scope: "global" | "custom";
  qrn: string; // Qualified Resource Name (e.g., 'org:123', 'aws:braket', 'user:self')
  resource: string; // Resource type (e.g., 'device', 'job', 'organization')
  action: string; // Action to perform (e.g., 'view', 'create', 'execute', 'manage')
}

/**
 * String-based permission format: scope|qrn|resource|action
 */
export type PermissionString = string;

/**
 * Role definition with permissions and inheritance
 */
export interface RoleDefinition {
  name: string;
  description?: string;
  permissions: PermissionString[];
  inheritFrom?: string[]; // Roles to inherit permissions from
  metadata?: Record<string, unknown>;
}

/**
 * Platform-wide roles
 */
export const PLATFORM_ROLES: Record<string, RoleDefinition> = {
  platform_admin: {
    name: "platform_admin",
    description: "Full platform access",
    permissions: ["global|*|*|*"],
  },
  platform_support: {
    name: "platform_support",
    description: "Support team access",
    permissions: [
      "global|*|logs|view",
      "global|*|jobs|view",
      "global|*|jobs|cancel",
      "global|*|devices|view",
      "global|*|devices|monitor",
      "global|*|users|view",
    ],
  },
  free_user: {
    name: "free_user",
    description: "Basic free tier",
    permissions: [
      "global|user:self|*|*",
      "global|simulator:*|device|execute",
      "global|*|courses|view",
      "global|*|courses|access",
    ],
  },
  premium_user: {
    name: "premium_user",
    description: "Premium tier",
    permissions: [
      "global|aws:braket:*|device|execute",
      "global|aws:ionq:*|device|execute",
      "global|*|courses|view",
      "global|*|courses|access",
      "global|*|advanced-courses|access",
    ],
    inheritFrom: ["free_user"],
  },
  enterprise_user: {
    name: "enterprise_user",
    description: "Enterprise tier",
    permissions: [
      "global|*|device|execute",
      "global|*|compute|priority",
      "global|*|support|priority",
    ],
    inheritFrom: ["premium_user"],
  },
};

/**
 * Organization roles
 */
export const ORGANIZATION_ROLES: Record<string, RoleDefinition> = {
  org_admin: {
    name: "org_admin",
    description: "Full org administration",
    permissions: [
      "global|org:*|organization|manage",
      "global|org:*|members|manage",
      "global|org:*|billing|manage",
      "global|org:*|resources|manage",
      "global|org:*|roles|manage",
    ],
  },
  org_manager: {
    name: "org_manager",
    description: "Resource management",
    permissions: [
      "global|org:*|devices|manage",
      "global|org:*|jobs|manage",
      "global|org:*|projects|manage",
      "global|org:*|members|view",
    ],
  },
  org_member: {
    name: "org_member",
    description: "Basic org access",
    permissions: [
      "global|org:*|resources|view",
      "global|org:*|jobs|submit",
      "global|org:*|projects|view",
    ],
  },
};

/**
 * Specialized roles
 */
export const SPECIALIZED_ROLES: Record<string, RoleDefinition> = {
  researcher: {
    name: "researcher",
    description: "Research-focused",
    permissions: [
      "global|*|compute|advanced",
      "global|*|datasets|access",
      "global|*|experiments|manage",
      "global|*|results|export",
    ],
    inheritFrom: ["premium_user"],
  },
  developer: {
    name: "developer",
    description: "Development-focused",
    permissions: [
      "global|*|api|access",
      "global|*|sdk|access",
      "global|*|development|access",
      "global|*|code|manage",
    ],
    inheritFrom: ["premium_user"],
  },
  auditor: {
    name: "auditor",
    description: "Read-only auditing",
    permissions: [
      "global|*|logs|view",
      "global|*|metrics|view",
      "global|*|reports|view",
      "global|*|audit|view",
    ],
  },
};

/**
 * All predefined roles combined
 */
export const ALL_ROLES = {
  ...PLATFORM_ROLES,
  ...ORGANIZATION_ROLES,
  ...SPECIALIZED_ROLES,
};

/**
 * Legacy role mappings for backward compatibility
 */
export const externalRoleToPermissions: Record<string, PermissionString[]> = {
  viewer: [
    "global|org:*|devices|view",
    "global|org:*|jobs|view",
    "global|org:*|projects|view",
    "global|org:*|profile|view",
    "global|org:*|earnings|view",
    "global|org:*|providers|view",
  ],
  manager: [
    "global|org:*|devices|view",
    "global|org:*|devices|manage",
    "global|org:*|jobs|view",
    "global|org:*|jobs|manage",
    "global|org:*|projects|view",
    "global|org:*|projects|manage",
    "global|org:*|team|view",
    "global|org:*|team|manage",
    "global|org:*|profile|view",
    "global|org:*|profile|edit",
    "global|org:*|earnings|view",
    "global|org:*|providers|view",
  ],
  admin: [
    "global|org:*|*|view",
    "global|org:*|*|manage",
    "global|org:*|billing|manage",
    "global|org:*|organization|manage",
  ],
  owner: [
    "global|org:*|*|*",
    "global|*|billing|manage",
    "global|*|organization|manage",
  ],
};

/**
 * Permission check result
 */
export interface PermissionCheckResult {
  granted: boolean;
  requiredPermission?: PermissionString;
  userPermissions?: PermissionString[];
  userRoles?: string[];
  reason?: string;
}

/**
 * Permission middleware options
 */
export interface PermissionMiddlewareOptions {
  requireAll?: boolean; // If true, user must have ALL permissions; if false, ANY permission
  organizationId?: string;
  customErrorMessage?: string;
}

/**
 * Custom permission definition
 */
export interface CustomPermission {
  id: string;
  qrn: string;
  resource: string;
  action: string;
  name: string;
  description?: string;
  category?: string;
  organizationId?: string;
  createdBy: string;
  createdAt: Date;
  isActive: boolean;
}

/**
 * Permission grant record
 */
export interface PermissionGrant {
  id: string;
  userId: string;
  permission: PermissionString;
  grantedBy: string;
  grantedAt: Date;
  expiresAt?: Date;
  organizationId?: string;
  reason?: string;
}

/**
 * Permission audit log entry
 */
export interface PermissionAuditLog {
  id: string;
  userId: string;
  action: "grant" | "revoke" | "check" | "deny";
  permission?: PermissionString;
  roles?: string[];
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  success: boolean;
  reason?: string;
}
/**
 * External role mapping configuration
 */
export interface ExternalRoleMapping {
  [externalRole: string]: PermissionString[];
}

/**
 * Session payload interface for JWT tokens - enhanced with RBAC
 */
export interface SessionPayload {
  name?: string;
  username: string;
  userId?: string;
  email: string;
  organizationRoles?: string[];
  permissions?: PermissionString[]; // User-specific permissions
  roles?: string[]; // User roles for permission inheritance
  organizationId?: string; // Current organization context
  signedIn: boolean;
  iat: number;
  exp: number;
  jti: string; // JWT ID for session tracking
  browserFingerprint?: string; // Browser fingerprint for session binding
}

/**
 * CSRF token interface for security validation
 */
export interface CSRFToken {
  token: string;
  exp: number;
}

/**
 * User data interface for session creation - enhanced with RBAC
 */
export interface UserSessionData {
  username: string;
  email: string;
  userId?: string;
  permissions?: PermissionString[];
  roles?: string[];
  organizationId?: string;
}

/**
 * External API role response interface
 */
export interface ExternalRoleResponse {
  roles: string[];
  email: string;
  timestamp?: number;
}
export interface Organization {
  org: {
    role: string;
    email: string;
    organization: {
      name: string;
      _id: string;
    };
  };
}

export interface OrganizationsResponse {
  organizations: Organization[];
  pagination: {
    currentPage: number;
    limit: string;
    totalPages: number;
    totalOrganizations: number;
  };
}

/**
 * Cognito tokens interface for AWS authentication
 */
export interface CognitoTokens {
  accessToken?: string;
  idToken?: string;
}

/**
 * Auth configuration interface for Amplify setup
 */
export interface AuthConfig {
  Auth: {
    Cognito: {
      userPoolId: string;
      userPoolClientId: string;
      identityPoolId: string;
      signUpVerificationMethod: "code" | "link";
      loginWith: {
        email: boolean;
        username: boolean;
        phone: boolean;
      };
    };
  };
  Storage?: unknown;
}

/**
 * Custom token storage interface for server-side operations
 */
export interface TokenStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

/**
 * Form state interface for authentication forms
 */
export interface AuthFormState {
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  nextStep?: string;
  redirectTo?: string;
  email?: string;
  csrfToken?: string;
}

/**
 * User details interface from Cognito
 */
export interface UserDetails {
  userId: string;
  username: string;
  email?: string;
  attributes?: Record<string, unknown>;
}

/**
 * Sign-in form data interface
 */
export interface SignInFormData {
  email: string;
  password: string;
  csrfToken: string;
}

/**
 * Sign-up form data interface
 */
export interface SignUpFormData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  csrfToken: string;
}

/**
 * Email verification form data interface
 */
export interface VerificationFormData {
  email: string;
  code: string;
  csrfToken: string;
}

/**
 * Password reset form data interface
 */
export interface PasswordResetFormData {
  email: string;
  code?: string;
  newPassword?: string;
  csrfToken: string;
}

/**
 * Authentication error types from Cognito
 */
export type CognitoErrorType =
  | "UserNotConfirmedException"
  | "NotAuthorizedException"
  | "UserNotFoundException"
  | "PasswordResetRequiredException"
  | "TooManyRequestsException"
  | "UnexpectedSignInInterruptionException"
  | "InvalidParameterException"
  | "UsernameExistsException"
  | "InvalidPasswordException"
  | "UserAlreadyAuthenticatedException";

/**
 * Authentication step types from Cognito
 */
export type AuthStepType =
  | "CONFIRM_SIGN_UP"
  | "RESET_PASSWORD"
  | "CONFIRM_PASSWORD_RESET"
  | "DONE";

/**
 * User status types
 */
export type UserStatus =
  | "CONFIRMED"
  | "UNCONFIRMED"
  | "ARCHIVED"
  | "COMPROMISED"
  | "UNKNOWN";

/**
 * Auth user interface for client-side authentication
 */
export interface AuthUser {
  username: string;
  email: string;
  attributes: {
    email: string;
    email_verified: boolean;
    sub: string;
    [key: string]: unknown;
  };
}

// External roles and user management types
export interface UserOrgRole {
  userId: string;
  orgId: string;
  role: "owner" | "admin" | "manager" | "viewer";
  status: "active" | "inactive" | "pending";
  joinedAt: Date;
  lastActive?: Date;
}

export interface UserOrgRoles {
  userId: string;
  organizations: UserOrgRole[];
}

export type UserRole = "owner" | "admin" | "manager" | "viewer";

export interface AddProviderFormProps {
  onSubmit: (data: AddProviderFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export interface AddProviderFormData {
  providerName: string;
  providerType: string;
  contactEmail: string;
  description?: string;
  website?: string;
  phoneNumber?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
}

export interface ProviderSubmissionResponse {
  success: boolean;
  message: string;
  providerId?: string;
  errors?: string[];
}

export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: Date;
}
