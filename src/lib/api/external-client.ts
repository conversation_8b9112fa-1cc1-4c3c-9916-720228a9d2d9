import axios, { AxiosInstance, AxiosError } from "axios";
import { getSession, getCognitoTokensBySessionId } from "@/lib/auth/session";

// Configuration for external API
// const baseURL = 'https://api.qbraid.com/api'; // Production URL
const baseURL = process.env.EXTERNAL_API_URL || "http://localhost:3001/api";

// Enable debug logging
const DEBUG =
    process.env.NEXT_PUBLIC_DEBUG === "true" ||
    process.env.NODE_ENV === "development";

// Types for API responses and errors
interface APIErrorResponse {
    message?: string;
    error?: string;
    [key: string]: unknown;
}

/**
 * Server-side HTTP client for external API
 * Automatically includes Bearer token and organization context
 */
class ExternalAPIClient {
    private client: AxiosInstance;

    constructor() {
        this.client = axios.create({
            baseURL,
            timeout: 30_000,
            headers: { "Content-Type": "application/json" },
        });

        this.setupInterceptors();
    }

    private setupInterceptors() {
        // Request interceptor for auth
        this.client.interceptors.request.use(
            async (config) => {
                const startTime = Date.now();

                try {
                    // Log request initiation
                    if (DEBUG) {
                        console.log(
                            `\n📤 [EXTERNAL-CLIENT] Preparing request:`,
                            {
                                method: config.method?.toUpperCase(),
                                url: config.url,
                                baseURL: config.baseURL,
                                fullURL: `${config.baseURL}${config.url}`,
                                hasData: !!config.data,
                                timestamp: new Date().toISOString(),
                            }
                        );
                    }

                    // Get current session from cookie
                    const session = await getSession();

                    if (DEBUG) {
                        console.log(`🔍 [EXTERNAL-CLIENT] Session check:`, {
                            found: !!session,
                            signedIn: session?.signedIn || false,
                            sessionId: session?.jti
                                ? `${session.jti.substring(0, 8)}...`
                                : null,
                            hasEmail: !!session?.email,
                            hasOrgId: !!session?.organizationId,
                        });
                    }

                    if (session?.signedIn && session.jti) {
                        // Get ID token from Redis using session ID
                        const tokens = await getCognitoTokensBySessionId(
                            session.jti
                        );

                        if (DEBUG) {
                            console.log(
                                `🔑 [EXTERNAL-CLIENT] Token retrieval:`,
                                {
                                    sessionId: `${session.jti.substring(0, 8)}...`,
                                    hasIdToken: !!tokens?.idToken,
                                    hasAccessToken: !!tokens?.accessToken,
                                    idTokenPreview: tokens?.idToken
                                        ? `${tokens.idToken.substring(0, 20)}...`
                                        : null,
                                }
                            );
                        }

                        if (tokens?.idToken) {
                            config.headers["Authorization"] =
                                `Bearer ${tokens.idToken}`;
                            console.log(
                                `🔐 [EXTERNAL-CLIENT] Added Bearer token to request`
                            );
                        } else {
                            console.warn(
                                `⚠️ [EXTERNAL-CLIENT] No ID token found for session`
                            );
                        }

                        // Add organization context if available
                        if (session.organizationId) {
                            config.headers["X-Organization-ID"] =
                                session.organizationId;
                            console.log(
                                `🏢 [EXTERNAL-CLIENT] Added organization ID: ${session.organizationId}`
                            );
                        }
                    } else {
                        console.warn(
                            `⚠️ [EXTERNAL-CLIENT] No valid session found`
                        );
                    }

                    // Log final request details
                    console.log(
                        `🔄 [External API] ${config.method?.toUpperCase()} ${config.url}`,
                        {
                            headers: {
                                Authorization: config.headers["Authorization"]
                                    ? "Bearer ***"
                                    : undefined,
                                "X-Organization-ID":
                                    config.headers["X-Organization-ID"],
                                "Content-Type": config.headers["Content-Type"],
                            },
                            duration: `${Date.now() - startTime}ms`,
                        }
                    );
                } catch (error) {
                    console.error(
                        "❌ [External API] Auth setup failed:",
                        error
                    );
                }

                return config;
            },
            (error) => {
                console.error(
                    "❌ [EXTERNAL-CLIENT] Request interceptor error:",
                    error
                );
                return Promise.reject(error);
            }
        );

        // Response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => {
                const duration = response.config.metadata?.startTime
                    ? Date.now() - response.config.metadata.startTime
                    : null;

                console.log(
                    `✅ [External API] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`,
                    {
                        statusText: response.statusText,
                        duration: duration ? `${duration}ms` : "unknown",
                        dataSize: JSON.stringify(response.data).length,
                        timestamp: new Date().toISOString(),
                    }
                );

                if (DEBUG && response.data) {
                    console.log(`📥 [EXTERNAL-CLIENT] Response preview:`, {
                        success: response.data.success,
                        hasData: !!response.data.data,
                        dataKeys: response.data.data
                            ? Object.keys(response.data.data)
                            : [],
                    });
                }

                return response;
            },
            (error: AxiosError) => {
                const duration = error.config?.metadata?.startTime
                    ? Date.now() - error.config.metadata.startTime
                    : null;

                console.error(
                    `❌ [External API] ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || "Network Error"}`,
                    {
                        statusText: error.response?.statusText,
                        duration: duration ? `${duration}ms` : "unknown",
                        errorCode: (error.response?.data as APIErrorResponse)
                            ?.error,
                        message: (error.response?.data as APIErrorResponse)
                            ?.message,
                        timestamp: new Date().toISOString(),
                    }
                );

                // Log specific error details based on status
                if (error.response?.status === 401) {
                    console.error(
                        `🔒 [EXTERNAL-CLIENT] Authentication Error:`,
                        {
                            reason: "Token may be expired or invalid",
                            headers: {
                                Authorization: error.config?.headers[
                                    "Authorization"
                                ]
                                    ? "Present"
                                    : "Missing",
                                "X-Organization-ID":
                                    error.config?.headers["X-Organization-ID"],
                            },
                        }
                    );
                } else if (error.response?.status === 403) {
                    console.error(`🚫 [EXTERNAL-CLIENT] Authorization Error:`, {
                        reason: "User lacks required permissions",
                        endpoint: error.config?.url,
                    });
                } else if (error.response?.status === 404) {
                    console.error(`🔍 [EXTERNAL-CLIENT] Not Found:`, {
                        endpoint: error.config?.url,
                        method: error.config?.method,
                    });
                } else if (error.response?.status >= 500) {
                    console.error(`💥 [EXTERNAL-CLIENT] Server Error:`, {
                        status: error.response.status,
                        data: error.response.data,
                    });
                } else if (!error.response) {
                    console.error(`🌐 [EXTERNAL-CLIENT] Network Error:`, {
                        message: error.message,
                        code: error.code,
                    });
                }

                // Transform external API errors to consistent format
                const errorResponse = {
                    error: true,
                    message:
                        (error.response?.data as APIErrorResponse)?.message ||
                        error.message ||
                        "External API error",
                    status: error.response?.status || 500,
                    data: error.response?.data,
                };

                return Promise.reject(errorResponse);
            }
        );
    }

    // Generic methods for different HTTP verbs
    async get<T = any>(url: string, params?: Record<string, unknown>) {
        const response = await this.client.get<T>(url, { params });
        return response.data;
    }

    async post<T = any>(url: string, data?: Record<string, unknown>) {
        const response = await this.client.post<T>(url, data);
        return response.data;
    }

    async put<T = any>(url: string, data?: Record<string, unknown>) {
        const response = await this.client.put<T>(url, data);
        return response.data;
    }

    async patch<T = any>(url: string, data?: Record<string, unknown>) {
        const response = await this.client.patch<T>(url, data);
        return response.data;
    }

    async delete<T = any>(url: string) {
        const response = await this.client.delete<T>(url);
        return response.data;
    }

    // Method to manually set organization context for a request
    async withOrganization<T = any>(
        organizationId: string,
        request: () => Promise<T>
    ): Promise<T> {
        // Temporarily set organization header
        const originalTransformRequest = this.client.defaults.transformRequest;

        this.client.defaults.transformRequest = [
            (data, headers) => {
                headers["X-Organization-ID"] = organizationId;
                return data;
            },
            ...(Array.isArray(originalTransformRequest)
                ? originalTransformRequest
                : [originalTransformRequest].filter(Boolean)),
        ];

        try {
            return await request();
        } finally {
            // Restore original transform
            this.client.defaults.transformRequest = originalTransformRequest;
        }
    }
}

// Export singleton instance
export const externalClient = new ExternalAPIClient();
export default externalClient;

/**
 * Helper function to get auth headers for manual fetch calls
 */
export async function getAuthHeaders(
    organizationId?: string
): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
        "Content-Type": "application/json",
    };

    try {
        const session = await getSession();

        if (session?.signedIn && session.jti) {
            const tokens = await getCognitoTokensBySessionId(session.jti);

            if (tokens?.idToken) {
                headers["Authorization"] = `Bearer ${tokens.idToken}`;
            }

            // Add organization context
            const orgId = organizationId || session.organizationId;
            if (orgId) {
                headers["X-Organization-ID"] = orgId;
            }
        }

        return headers;
    } catch (error) {
        console.error("❌ [AUTH-HEADERS] Failed to get auth headers:", error);
        return headers;
    }
}
