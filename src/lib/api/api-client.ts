"use client";

import axios, { AxiosInstance, AxiosError } from "axios";

// Enable debug logging in development
const DEBUG = process.env.NODE_ENV === "development";

// Types for API responses and errors
interface APIErrorResponse {
    message?: string;
    error?: string;
    [key: string]: unknown;
}

interface APIRequest {
    organizationId?: string;
    [key: string]: any;
}

/**
 * Client-side HTTP client for internal API
 * Automatically includes session ID from cookie and organization context
 */
class APIClient {
    private client: AxiosInstance;
    private organizationId?: string;

    constructor() {
        console.log("🚀 [API-CLIENT] Initializing client-side API client");

        this.client = axios.create({
            baseURL: "/api", // Internal API routes (Next.js backend)
            timeout: 30_000,
            headers: {
                "Content-Type": "application/json",
            },
            withCredentials: true, // Include cookies (session ID)
        });

        this.setupInterceptors();

        if (DEBUG) {
            console.log("📍 [API-CLIENT] Client configured:", {
                baseURL: "/api",
                timeout: "30s",
                withCredentials: true,
            });
        }
    }

    /**
     * Set the current organization context
     * This will be included in all subsequent requests
     */
    setOrganizationId(organizationId?: string) {
        const previous = this.organizationId;
        this.organizationId = organizationId;

        console.log("🏢 [API-CLIENT] Organization context updated:", {
            previous,
            current: organizationId,
        });
    }

    /**
     * Get the current organization context
     */
    getOrganizationId(): string | undefined {
        return this.organizationId;
    }

    private setupInterceptors() {
        // Request interceptor to add organization context
        this.client.interceptors.request.use(
            (config) => {
                const startTime = Date.now();

                // Store start time for duration calculation
                config.metadata = { startTime };

                // Log request details
                if (DEBUG) {
                    console.log(`\n📤 [API-CLIENT] Preparing request:`, {
                        method: config.method?.toUpperCase(),
                        url: config.url,
                        fullURL: `${window.location.origin}${config.url}`,
                        hasData: !!config.data,
                        dataKeys: config.data ? Object.keys(config.data) : [],
                        organizationContext: this.organizationId || "none",
                    });
                }

                // Add organization ID to request body if available
                if (
                    this.organizationId &&
                    config.data &&
                    typeof config.data === "object"
                ) {
                    config.data = {
                        ...config.data,
                        organizationId: this.organizationId,
                    };

                    console.log(
                        `📎 [API-CLIENT] Added organization ID to request body:`,
                        this.organizationId
                    );
                }

                // Session ID is automatically sent via httpOnly cookie
                console.log(
                    `🔄 [API] ${config.method?.toUpperCase()} ${config.url}`,
                    {
                        withCredentials: config.withCredentials,
                        hasOrgId: !!this.organizationId,
                        timestamp: new Date().toISOString(),
                    }
                );

                return config;
            },
            (error) => {
                console.error("❌ [API-CLIENT] Request setup error:", error);
                return Promise.reject(error);
            }
        );

        // Response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => {
                const duration = response.config.metadata?.startTime
                    ? Date.now() - response.config.metadata.startTime
                    : null;

                console.log(
                    `✅ [API] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`,
                    {
                        duration: duration ? `${duration}ms` : "unknown",
                        dataSize: response.data
                            ? JSON.stringify(response.data).length
                            : 0,
                        timestamp: new Date().toISOString(),
                    }
                );

                if (DEBUG && response.data) {
                    console.log(`📥 [API-CLIENT] Response preview:`, {
                        success:
                            response.data.success !== undefined
                                ? response.data.success
                                : true,
                        hasData: !!response.data.data || !!response.data,
                        dataKeys: Object.keys(response.data),
                    });
                }

                return response;
            },
            async (error: AxiosError) => {
                const originalRequest = error.config;
                const duration = error.config?.metadata?.startTime
                    ? Date.now() - error.config.metadata.startTime
                    : null;

                // Log error details
                console.error(
                    `❌ [API] ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || "Network Error"}`,
                    {
                        duration: duration ? `${duration}ms` : "unknown",
                        message:
                            (error.response?.data as APIErrorResponse)
                                ?.message || error.message,
                        timestamp: new Date().toISOString(),
                    }
                );

                // Handle specific error cases
                if (error.response?.status === 401 && originalRequest) {
                    console.log("🔒 [API-CLIENT] Authentication Error:", {
                        reason: "Session expired or invalid",
                        redirecting: true,
                        currentPath: window.location.pathname,
                    });

                    // Only redirect if not already on auth pages
                    if (!window.location.pathname.startsWith("/signin")) {
                        console.log("🔄 [API-CLIENT] Redirecting to login...");
                        window.location.href = "/signin";
                    }

                    return Promise.reject(error);
                } else if (error.response?.status === 403) {
                    console.error("🚫 [API-CLIENT] Authorization Error:", {
                        reason: "Insufficient permissions",
                        endpoint: error.config?.url,
                        organizationId: this.organizationId,
                    });
                } else if (error.response?.status === 404) {
                    console.error("🔍 [API-CLIENT] Not Found:", {
                        endpoint: error.config?.url,
                        method: error.config?.method,
                    });
                } else if (error.response?.status >= 500) {
                    console.error("💥 [API-CLIENT] Server Error:", {
                        status: error.response.status,
                        message: (error.response?.data as APIErrorResponse)
                            ?.message,
                    });
                } else if (!error.response) {
                    console.error("🌐 [API-CLIENT] Network Error:", {
                        message: error.message,
                        code: error.code,
                        baseURL: error.config?.baseURL,
                        url: error.config?.url,
                    });
                }

                // Transform API errors to consistent format
                const errorResponse = {
                    error: true,
                    message:
                        (error.response?.data as APIErrorResponse)?.message ||
                        (error.response?.data as APIErrorResponse)?.error ||
                        error.message ||
                        "API error",
                    status: error.response?.status || 500,
                    data: error.response?.data,
                };

                return Promise.reject(errorResponse);
            }
        );
    }

    // Generic methods for different HTTP verbs
    async get<T = any>(url: string, params?: Record<string, unknown>) {
        const response = await this.client.get<T>(url, { params });
        return response.data;
    }

    async post<T = any>(url: string, data?: APIRequest) {
        const response = await this.client.post<T>(url, data);
        return response.data;
    }

    async put<T = any>(url: string, data?: APIRequest) {
        const response = await this.client.put<T>(url, data);
        return response.data;
    }

    async patch<T = any>(url: string, data?: APIRequest) {
        const response = await this.client.patch<T>(url, data);
        return response.data;
    }

    async delete<T = any>(url: string) {
        const response = await this.client.delete<T>(url);
        return response.data;
    }
}

// Export singleton instance
export const apiClient = new APIClient();
export default apiClient;
