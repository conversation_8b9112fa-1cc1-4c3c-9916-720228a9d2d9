import { externalClient } from "./external-client";

/**
 * User API service using the external client
 */
export const userAPI = {
    /**
     * Get user profile from external API
     */
    async getProfile(email: string) {
        return externalClient.get(
            `/v1/users/${encodeURIComponent(email)}/context`
        );
    },

    /**
     * Get user profile for specific organization
     */
    async getProfileForOrg(email: string, organizationId: string) {
        return externalClient.get(
            `/v1/users/${encodeURIComponent(email)}/organizations/${organizationId}/context`
        );
    },

    /**
     * Update user profile
     */
    async updateProfile(email: string, data: any) {
        return externalClient.patch(
            `/v1/users/${encodeURIComponent(email)}`,
            data
        );
    },

    /**
     * Get user permissions
     */
    async getPermissions(email: string) {
        const profile = await this.getProfile(email);
        return profile?.data?.permissions || [];
    },

    /**
     * Switch user organization (updates session context)
     */
    async switchOrganization(organizationId: string) {
        // This would be called from the client-side through our own API
        return externalClient.post("/v1/users/switch-organization", {
            organizationId,
        });
    },
};
