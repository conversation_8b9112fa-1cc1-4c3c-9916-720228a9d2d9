"use client";

import { apiClient } from '../api-client';

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  username: string;
  permissions: string[];
  roles: string[];
  organizations?: any;
  currentOrganizationId?: string;
}

/**
 * Client-side user API service
 */
export const userAPI = {
  /**
   * Get current user profile
   */
  async getProfile(): Promise<{ success: boolean; data: UserProfile }> {
    return apiClient.get('/user/profile');
  },

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<UserProfile>) {
    return apiClient.patch('/user/profile', data);
  },

  /**
   * Get user settings
   */
  async getSettings() {
    return apiClient.get('/user/settings');
  },

  /**
   * Update user settings
   */
  async updateSettings(settings: any) {
    return apiClient.put('/user/settings', settings);
  },

  /**
   * Get user notifications
   */
  async getNotifications() {
    return apiClient.get('/user/notifications');
  },

  /**
   * Mark notification as read
   */
  async markNotificationRead(notificationId: string) {
    return apiClient.patch(`/user/notifications/${notificationId}/read`);
  },

  /**
   * Get user's organizations
   */
  async getOrganizations() {
    return apiClient.get('/user/organizations');
  },

  /**
   * Get user activity/audit log
   */
  async getActivity(params?: { limit?: number; offset?: number }) {
    return apiClient.get('/user/activity', params);
  }
};
