"use client";

import { apiClient } from '../api-client';

export interface SignInCredentials {
  email: string;
  password: string;
}

export interface SignInResponse {
  success: boolean;
  user: {
    id: string;
    email: string;
    name: string;
    permissions: string[];
    roles: string[];
    organizationId?: string;
  };
}

/**
 * Client-side auth API service
 */
export const authAPI = {
  /**
   * Sign in with email and password
   */
  async signIn(credentials: SignInCredentials): Promise<SignInResponse> {
    return apiClient.post('/auth/signin', credentials);
  },

  /**
   * Sign out
   */
  async signOut() {
    return apiClient.post('/auth/signout');
  },

  /**
   * Get current session
   */
  async getSession() {
    return apiClient.get('/auth/session');
  },

  /**
   * Switch organization
   */
  async switchOrganization(organizationId: string) {
    const response = await apiClient.post('/auth/switch-organization', { 
      organizationId 
    });
    
    // Update the client's organization context
    if (response.success) {
      apiClient.setOrganizationId(organizationId);
    }
    
    return response;
  },

  /**
   * Request password reset
   */
  async forgotPassword(email: string) {
    return apiClient.post('/auth/forgot-password', { email });
  },

  /**
   * Reset password with token
   */
  async resetPassword(token: string, password: string) {
    return apiClient.post('/auth/reset-password', { token, password });
  },

  /**
   * Verify email with token
   */
  async verifyEmail(token: string) {
    return apiClient.post('/auth/verify', { token });
  },

  /**
   * Get CSRF token for server actions
   */
  async getCSRFToken() {
    return apiClient.get('/auth/csrf-token');
  }
};
