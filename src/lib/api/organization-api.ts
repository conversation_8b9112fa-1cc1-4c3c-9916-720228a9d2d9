import { externalClient } from './external-client';

/**
 * Organization API service
 */
export const organizationAPI = {
  /**
   * Get organization details
   */
  async getOrganization(organizationId: string) {
    return externalClient.get(`/v1/organizations/${organizationId}`);
  },

  /**
   * List user's organizations
   */
  async listUserOrganizations(email: string) {
    const profile = await externalClient.get(`/v1/users/${encodeURIComponent(email)}/context`);
    return profile?.data?.organizations || {};
  },

  /**
   * Get organization members
   */
  async getMembers(organizationId: string) {
    // Use withOrganization to ensure org context is sent
    return externalClient.withOrganization(organizationId, () =>
      externalClient.get(`/v1/organizations/${organizationId}/members`)
    );
  },

  /**
   * Invite user to organization
   */
  async inviteUser(organizationId: string, email: string, role: string) {
    return externalClient.withOrganization(organizationId, () =>
      externalClient.post(`/v1/organizations/${organizationId}/invites`, {
        email,
        role,
      })
    );
  },

  /**
   * Update organization settings
   */
  async updateSettings(organizationId: string, settings: any) {
    return externalClient.withOrganization(organizationId, () =>
      externalClient.patch(`/v1/organizations/${organizationId}/settings`, settings)
    );
  },

  /**
   * Get organization usage/billing
   */
  async getUsage(organizationId: string) {
    return externalClient.withOrganization(organizationId, () =>
      externalClient.get(`/v1/organizations/${organizationId}/usage`)
    );
  }
};
