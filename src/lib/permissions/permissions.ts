import {
  PermissionString,
  Permission,
  PermissionCheckResult,
  PermissionMiddlewareOptions,
  ALL_ROLES as AUTHZ_ROLES,
  externalRoleToPermissions,
} from "../../types/auth";
import {
  ROLE_HIERARCHY,
  ALL_ROLES as LEGACY_ROLES,
  ROLE_MANAGEMENT_PERMISSIONS,
  ROUTE_PERMISSIONS,
  ROLE_DISPLAY_NAMES,
  ROLE_DESCRIPTIONS,
} from "../utils/constant";

// Re-export constants for backward compatibility

// ============================================================================
// ROUTE PROTECTION
// ============================================================================

/**
 * Maps application routes to required permissions (converted from string constants to Permission enum)
 */
export const routePermissions: Record<string, PermissionString> =
  Object.fromEntries(
    Object.entries(ROUTE_PERMISSIONS).map(([route, permission]) => [
      route,
      permission as string,
    ]),
  );

// ============================================================================
// PERMISSION CHECK FUNCTIONS
// ============================================================================

/**
 * Check if a user can change another user's role
 *
 * @param currentUserRole - The role of the user attempting the change
 * @param targetUserRole - The current role of the user being changed
 * @param newRole - The role to assign to the target user
 * @returns Object with canChange boolean and optional reason for denial
 */
export function canChangeUserRole(
  currentUserRole: string,
  targetUserRole: string,
  newRole: string,
): { canChange: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();
  const normalizedNewRole = newRole.toLowerCase();

  // Get hierarchy levels
  const currentLevel = ROLE_HIERARCHY[normalizedCurrentRole] || 0;
  const targetLevel = ROLE_HIERARCHY[normalizedTargetRole] || 0;
  const newLevel = ROLE_HIERARCHY[normalizedNewRole] || 0;

  // Check management permissions
  const managableRoles =
    ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];

  // Validation checks
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to manage users with ${targetUserRole} role`,
    };
  }

  if (!managableRoles.includes(normalizedNewRole)) {
    return {
      canChange: false,
      reason: `You don't have permission to assign ${newRole} role`,
    };
  }

  if (newLevel >= currentLevel) {
    return {
      canChange: false,
      reason: `You cannot promote someone to a role equal or higher than your own`,
    };
  }

  return { canChange: true };
}

/**
 * Check if a user can remove another user from the organization
 *
 * @param currentUserRole - The role of the user attempting removal
 * @param targetUserRole - The role of the user to be removed
 * @returns Object with canRemove boolean and optional reason for denial
 */
export function canRemoveUser(
  currentUserRole: string,
  targetUserRole: string,
): { canRemove: boolean; reason?: string } {
  const normalizedCurrentRole = currentUserRole.toLowerCase();
  const normalizedTargetRole = targetUserRole.toLowerCase();

  // Special rule: Owners cannot be removed
  if (normalizedTargetRole === "owner") {
    return {
      canRemove: false,
      reason: "Organization owners cannot be removed",
    };
  }

  // Check management permissions
  const managableRoles =
    ROLE_MANAGEMENT_PERMISSIONS[normalizedCurrentRole] || [];
  if (!managableRoles.includes(normalizedTargetRole)) {
    return {
      canRemove: false,
      reason: `You don't have permission to remove users with ${targetUserRole} role`,
    };
  }

  return { canRemove: true };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get list of roles that a user can assign to others
 *
 * @param currentUserRole - The role of the user
 * @returns Array of role names that can be assigned
 */
export function getAssignableRoles(currentUserRole: string): string[] {
  const normalizedRole = currentUserRole.toLowerCase();

  // Owners can assign any role
  if (normalizedRole === "owner") {
    return LEGACY_ROLES;
  }

  return ROLE_MANAGEMENT_PERMISSIONS[normalizedRole] || [];
}

/**
 * Convert external role names to internal permission sets
 *
 * @param externalRoles - Single role or array of role names from external API
 * @returns Array of unique permissions
 */
export function mapRolesToPermissions(
  externalRoles: string | string[],
): PermissionString[] {
  if (typeof externalRoles === "string") {
    return externalRoleToPermissions[externalRoles?.toLowerCase()] || [];
  }

  // Handle array of roles - flatten and deduplicate permissions
  const allPermissions = externalRoles.flatMap(
    (role) => externalRoleToPermissions[role?.toLowerCase()] || [],
  );

  // Remove duplicates manually for better compatibility
  const uniquePermissions: PermissionString[] = [];
  for (const permission of allPermissions) {
    if (!uniquePermissions.includes(permission)) {
      uniquePermissions.push(permission);
    }
  }
  return uniquePermissions;
}

/**
 * Check if user has a specific permission
 *
 * @param userPermissions - User's permission array
 * @param requiredPermission - Permission to check for
 * @returns true if user has permission or admin access
 */
export function hasPermission(
  userPermissions: PermissionString[],
  requiredPermission: PermissionString,
): boolean {
  return userPermissions.includes(requiredPermission);
}

/**
 * Check if user has any of the required permissions
 *
 * @param userPermissions - User's permission array
 * @param requiredPermissions - Array of permissions (user needs at least one)
 * @returns true if user has any of the required permissions
 */
export function hasAnyPermission(
  userPermissions: PermissionString[],
  requiredPermissions: PermissionString[],
): boolean {
  return requiredPermissions.some((permission) =>
    hasPermission(userPermissions, permission),
  );
}

/**
 * Get required permission for a specific route
 *
 * @param pathname - The route path to check
 * @returns Required permission or null if route is unprotected
 */
export function getRoutePermission(pathname: string): PermissionString | null {
  // Exact match
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Prefix match (for nested routes)
  for (const [route, permission] of Object.entries(routePermissions)) {
    if (pathname.startsWith(route)) {
      return permission;
    }
  }

  return null;
}

// ============================================================================
// HELPER FUNCTIONS FOR UI
// ============================================================================

/**
 * Get human-readable role display name
 */
export function getRoleDisplayName(role: string): string {
  return ROLE_DISPLAY_NAMES[role.toLowerCase()] || role;
}

/**
 * Get role description for UI tooltips
 */
export function getRoleDescription(role: string): string {
  return ROLE_DESCRIPTIONS[role.toLowerCase()] || "Custom role";
}

// ============================================================================
// ENHANCED AUTHZ PERMISSION SYSTEM
// ============================================================================

/**
 * Parse a permission string into its components
 */
export function parsePermission(permissionStr: PermissionString): Permission {
  const parts = permissionStr.split("|");
  if (parts.length !== 4) {
    throw new Error(`Invalid permission format: ${permissionStr}`);
  }

  const [scope, qrn, resource, action] = parts;
  if (!scope || !qrn || !resource || !action) {
    throw new Error(`Invalid permission format: ${permissionStr}`);
  }

  if (!["global", "custom"].includes(scope)) {
    throw new Error(`Invalid scope: ${scope}`);
  }

  return { scope: scope as "global" | "custom", qrn, resource, action };
}

/**
 * Convert a permission object back to string format
 */
export function stringifyPermission(permission: Permission): PermissionString {
  return `${permission.scope}|${permission.qrn}|${permission.resource}|${permission.action}`;
}

/**
 * Check if a permission matches a pattern (supports wildcards)
 */
export function permissionMatches(
  userPermission: PermissionString,
  requiredPermission: PermissionString,
): boolean {
  const userPerm = parsePermission(userPermission);
  const requiredPerm = parsePermission(requiredPermission);

  // Check scope
  if (userPerm.scope !== requiredPerm.scope && userPerm.scope !== "global") {
    return false;
  }

  // Check QRN with wildcard support
  if (!matchesWithWildcard(userPerm.qrn, requiredPerm.qrn)) {
    return false;
  }

  // Check resource with wildcard support
  if (!matchesWithWildcard(userPerm.resource, requiredPerm.resource)) {
    return false;
  }

  // Check action with wildcard support
  if (!matchesWithWildcard(userPerm.action, requiredPerm.action)) {
    return false;
  }

  return true;
}

/**
 * Check if a string matches a pattern with wildcards
 */
function matchesWithWildcard(value: string, pattern: string): boolean {
  if (pattern === "*") return true;
  if (value === pattern) return true;

  // Handle wildcard patterns like 'aws:*'
  if (pattern.includes("*")) {
    const regexPattern = pattern.replace(/\*/g, ".*");
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(value);
  }

  return false;
}

/**
 * Resolve role inheritance and get all permissions for a role
 */
export function resolveRolePermissions(roleName: string): PermissionString[] {
  const role = AUTHZ_ROLES[roleName];
  if (!role) {
    console.warn(`Role not found: ${roleName}`);
    return [];
  }

  const permissions = new Set(role.permissions);

  // Add inherited permissions
  if (role.inheritFrom) {
    for (const inheritedRole of role.inheritFrom) {
      const inheritedPermissions = resolveRolePermissions(inheritedRole);
      inheritedPermissions.forEach((perm) => permissions.add(perm));
    }
  }

  return Array.from(permissions);
}

/**
 * Enhanced permission check with role support
 */
export function hasPermissionEnhanced(
  userPermissions: PermissionString[],
  userRoles: string[],
  requiredPermission: PermissionString,
  organizationId?: string,
): PermissionCheckResult {
  // Check direct permissions
  for (const userPerm of userPermissions) {
    if (permissionMatches(userPerm, requiredPermission)) {
      return {
        granted: true,
        requiredPermission,
        userPermissions,
        userRoles,
      };
    }
  }

  // Check role-based permissions
  for (const roleName of userRoles) {
    const rolePermissions = resolveRolePermissions(roleName);
    for (const rolePerm of rolePermissions) {
      if (permissionMatches(rolePerm, requiredPermission)) {
        return {
          granted: true,
          requiredPermission,
          userPermissions,
          userRoles,
        };
      }
    }
  }

  return {
    granted: false,
    requiredPermission,
    userPermissions,
    userRoles,
    reason: "Permission not found in user permissions or roles",
  };
}

/**
 * Check if user has any of the required permissions (enhanced)
 */
export function hasAnyPermissionEnhanced(
  userPermissions: PermissionString[],
  userRoles: string[],
  requiredPermissions: PermissionString[],
  organizationId?: string,
): PermissionCheckResult {
  for (const requiredPerm of requiredPermissions) {
    const result = hasPermissionEnhanced(
      userPermissions,
      userRoles,
      requiredPerm,
      organizationId,
    );
    if (result.granted) {
      return result;
    }
  }

  return {
    granted: false,
    requiredPermission: requiredPermissions.join(" OR "),
    userPermissions,
    userRoles,
    reason: "None of the required permissions found",
  };
}

/**
 * Check if user has all of the required permissions (enhanced)
 */
export function hasAllPermissionsEnhanced(
  userPermissions: PermissionString[],
  userRoles: string[],
  requiredPermissions: PermissionString[],
  organizationId?: string,
): PermissionCheckResult {
  for (const requiredPerm of requiredPermissions) {
    const result = hasPermissionEnhanced(
      userPermissions,
      userRoles,
      requiredPerm,
      organizationId,
    );
    if (!result.granted) {
      return {
        granted: false,
        requiredPermission: requiredPerm,
        userPermissions,
        userRoles,
        reason: `Missing permission: ${requiredPerm}`,
      };
    }
  }

  return {
    granted: true,
    requiredPermission: requiredPermissions.join(" AND "),
    userPermissions,
    userRoles,
  };
}

/**
 * Check if user has a specific role
 */
export function hasRole(userRoles: string[], requiredRole: string): boolean {
  return userRoles.includes(requiredRole);
}

/**
 * Check if user has any of the required roles
 */
export function hasAnyRole(
  userRoles: string[],
  requiredRoles: string[],
): boolean {
  return requiredRoles.some((role) => userRoles.includes(role));
}

/**
 * Check permissions or roles (OR condition)
 */
export function hasPermissionsOrRoles(
  userPermissions: PermissionString[],
  userRoles: string[],
  requiredPermissions: PermissionString[],
  requiredRoles: string[],
  organizationId?: string,
): PermissionCheckResult {
  // Check permissions first
  const permResult = hasAnyPermissionEnhanced(
    userPermissions,
    userRoles,
    requiredPermissions,
    organizationId,
  );
  if (permResult.granted) {
    return permResult;
  }

  // Check roles
  if (hasAnyRole(userRoles, requiredRoles)) {
    return {
      granted: true,
      userPermissions,
      userRoles,
      reason: `Granted via role: ${requiredRoles.find((role) => userRoles.includes(role))}`,
    };
  }

  return {
    granted: false,
    requiredPermission: `${requiredPermissions.join(" OR ")} OR roles: ${requiredRoles.join(" OR ")}`,
    userPermissions,
    userRoles,
    reason: "Neither required permissions nor roles found",
  };
}

/**
 * Get all effective permissions for a user (including role-based)
 */
export function getEffectivePermissions(
  userPermissions: PermissionString[],
  userRoles: string[],
): PermissionString[] {
  const effectivePermissions = new Set(userPermissions);

  // Add permissions from roles
  for (const roleName of userRoles) {
    const rolePermissions = resolveRolePermissions(roleName);
    rolePermissions.forEach((perm) => effectivePermissions.add(perm));
  }

  return Array.from(effectivePermissions);
}

/**
 * Validate permission string format
 */
export function isValidPermissionString(permissionStr: string): boolean {
  try {
    parsePermission(permissionStr);
    return true;
  } catch {
    return false;
  }
}

/**
 * Filter permissions by organization
 */
export function filterPermissionsByOrganization(
  permissions: PermissionString[],
  organizationId: string,
): PermissionString[] {
  return permissions.filter((perm) => {
    const parsed = parsePermission(perm);
    return (
      parsed.qrn === `org:${organizationId}` ||
      parsed.qrn === "org:*" ||
      parsed.qrn === "*"
    );
  });
}

// ============================================================================
// MIDDLEWARE FUNCTIONS
// ============================================================================

/**
 * Middleware function to require a specific permission
 */
export function requirePermission(
  permissionGetter: (req: any) => PermissionString,
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userPermissions = JSON.parse(
        req.headers["x-user-permissions"] || "[]",
      );
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");
      const organizationId = req.headers["x-organization-id"];

      const requiredPermission = permissionGetter(req);
      const result = hasPermissionEnhanced(
        userPermissions,
        userRoles,
        requiredPermission,
        organizationId,
      );

      if (!result.granted) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required permission: ${requiredPermission}`,
          code: "INSUFFICIENT_PERMISSIONS",
          requiredPermission,
          userRoles,
          userPermissions,
        });
      }

      next();
    } catch (error) {
      console.error("Permission middleware error:", error);
      res.status(500).json({
        error: "Permission validation failed",
        message: "Internal server error during permission check",
      });
    }
  };
}

/**
 * Middleware function to require any of multiple permissions
 */
export function requireAnyPermission(
  permissions: PermissionString[],
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userPermissions = JSON.parse(
        req.headers["x-user-permissions"] || "[]",
      );
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");
      const organizationId = req.headers["x-organization-id"];

      const result = hasAnyPermissionEnhanced(
        userPermissions,
        userRoles,
        permissions,
        organizationId,
      );

      if (!result.granted) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required permissions: ${permissions.join(" OR ")}`,
          code: "INSUFFICIENT_PERMISSIONS",
          requiredPermissions: permissions,
          userRoles,
          userPermissions,
        });
      }

      next();
    } catch (error) {
      console.error("Permission middleware error:", error);
      res.status(500).json({
        error: "Permission validation failed",
        message: "Internal server error during permission check",
      });
    }
  };
}

/**
 * Middleware function to require all permissions
 */
export function requireAllPermissions(
  permissions: PermissionString[],
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userPermissions = JSON.parse(
        req.headers["x-user-permissions"] || "[]",
      );
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");
      const organizationId = req.headers["x-organization-id"];

      const result = hasAllPermissionsEnhanced(
        userPermissions,
        userRoles,
        permissions,
        organizationId,
      );

      if (!result.granted) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required permissions: ${permissions.join(" AND ")}`,
          code: "INSUFFICIENT_PERMISSIONS",
          requiredPermissions: permissions,
          userRoles,
          userPermissions,
        });
      }

      next();
    } catch (error) {
      console.error("Permission middleware error:", error);
      res.status(500).json({
        error: "Permission validation failed",
        message: "Internal server error during permission check",
      });
    }
  };
}

/**
 * Middleware function to require a specific role
 */
export function requireRole(
  requiredRole: string,
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");

      if (!hasRole(userRoles, requiredRole)) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required role: ${requiredRole}`,
          code: "INSUFFICIENT_ROLE",
          requiredRole,
          userRoles,
        });
      }

      next();
    } catch (error) {
      console.error("Role middleware error:", error);
      res.status(500).json({
        error: "Role validation failed",
        message: "Internal server error during role check",
      });
    }
  };
}

/**
 * Middleware function to require any of multiple roles
 */
export function requireAnyRole(
  requiredRoles: string[],
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");

      if (!hasAnyRole(userRoles, requiredRoles)) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required roles: ${requiredRoles.join(" OR ")}`,
          code: "INSUFFICIENT_ROLE",
          requiredRoles,
          userRoles,
        });
      }

      next();
    } catch (error) {
      console.error("Role middleware error:", error);
      res.status(500).json({
        error: "Role validation failed",
        message: "Internal server error during role check",
      });
    }
  };
}

/**
 * Middleware function to require permissions OR roles
 */
export function requirePermissionsOrRoles(
  permissions: PermissionString[],
  roles: string[],
  options: PermissionMiddlewareOptions = {},
) {
  return async (req: any, res: any, next: any) => {
    try {
      const userPermissions = JSON.parse(
        req.headers["x-user-permissions"] || "[]",
      );
      const userRoles = JSON.parse(req.headers["x-user-roles"] || "[]");
      const organizationId = req.headers["x-organization-id"];

      const result = hasPermissionsOrRoles(
        userPermissions,
        userRoles,
        permissions,
        roles,
        organizationId,
      );

      if (!result.granted) {
        return res.status(403).json({
          error: "Forbidden",
          message:
            options.customErrorMessage ||
            `Missing required permissions or roles`,
          code: "INSUFFICIENT_PERMISSIONS_OR_ROLES",
          requiredPermissions: permissions,
          requiredRoles: roles,
          userRoles,
          userPermissions,
        });
      }

      next();
    } catch (error) {
      console.error("Permissions/Roles middleware error:", error);
      res.status(500).json({
        error: "Permission/Role validation failed",
        message: "Internal server error during validation",
      });
    }
  };
}

// ============================================================================
// LEGACY EXPORTS FOR BACKWARD COMPATIBILITY
// ============================================================================

export {
  ROLE_HIERARCHY,
  ALL_ROLES,
  ROLE_MANAGEMENT_PERMISSIONS,
} from "../utils/constant";
export { externalRoleToPermissions } from "../../types/auth";
