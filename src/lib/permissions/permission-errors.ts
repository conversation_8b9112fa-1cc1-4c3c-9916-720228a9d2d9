import { PermissionCheckResult, PermissionString } from "@/types/auth";

/**
 * Permission error types
 */
export enum PermissionErrorType {
  INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
  INSUFFICIENT_ROLE = "INSUFFICIENT_ROLE",
  INSUFFICIENT_PERMISSIONS_OR_ROLES = "INSUFFICIENT_PERMISSIONS_OR_ROLES",
  SESSION_EXPIRED = "SESSION_EXPIRED",
  INVALID_SESSION = "INVALID_SESSION",
  ORGANIZATION_ACCESS_DENIED = "ORGANIZATION_ACCESS_DENIED",
}

/**
 * Permission error response interface
 */
export interface PermissionErrorResponse {
  error: string;
  message: string;
  code: PermissionErrorType;
  details?: {
    requiredPermission?: PermissionString;
    requiredPermissions?: PermissionString[];
    requiredRole?: string;
    requiredRoles?: string[];
    userPermissions?: PermissionString[];
    userRoles?: string[];
    organizationId?: string;
    reason?: string;
  };
  timestamp: string;
}

/**
 * Create a permission error response
 */
export function createPermissionError(
  type: PermissionErrorType,
  message: string,
  details?: PermissionErrorResponse["details"],
): PermissionErrorResponse {
  return {
    error: "Forbidden",
    message,
    code: type,
    details,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create error response from permission check result
 */
export function createErrorFromPermissionCheck(
  result: PermissionCheckResult,
  context?: {
    organizationId?: string;
    requiredPermissions?: PermissionString[];
    requiredRoles?: string[];
  },
): PermissionErrorResponse {
  if (result.granted) {
    throw new Error("Cannot create error from successful permission check");
  }

  const details: PermissionErrorResponse["details"] = {
    userPermissions: result.userPermissions,
    userRoles: result.userRoles,
    reason: result.reason,
    organizationId: context?.organizationId,
  };

  if (result.requiredPermission) {
    details.requiredPermission = result.requiredPermission;
    return createPermissionError(
      PermissionErrorType.INSUFFICIENT_PERMISSIONS,
      `Missing required permission: ${result.requiredPermission}`,
      details,
    );
  }

  if (context?.requiredPermissions) {
    details.requiredPermissions = context.requiredPermissions;
    return createPermissionError(
      PermissionErrorType.INSUFFICIENT_PERMISSIONS,
      `Missing required permissions: ${context.requiredPermissions.join(" OR ")}`,
      details,
    );
  }

  if (context?.requiredRoles) {
    details.requiredRoles = context.requiredRoles;
    return createPermissionError(
      PermissionErrorType.INSUFFICIENT_ROLE,
      `Missing required roles: ${context.requiredRoles.join(" OR ")}`,
      details,
    );
  }

  return createPermissionError(
    PermissionErrorType.INSUFFICIENT_PERMISSIONS,
    "Access denied",
    details,
  );
}

/**
 * Permission error middleware for API routes
 */
export function handlePermissionError(
  error: any,
  context?: any,
): PermissionErrorResponse {
  if (error.code && Object.values(PermissionErrorType).includes(error.code)) {
    return error as PermissionErrorResponse;
  }

  // Handle common permission-related errors
  if (
    error.message?.includes("permission") ||
    error.message?.includes("Permission")
  ) {
    return createPermissionError(
      PermissionErrorType.INSUFFICIENT_PERMISSIONS,
      error.message,
      context,
    );
  }

  if (error.message?.includes("role") || error.message?.includes("Role")) {
    return createPermissionError(
      PermissionErrorType.INSUFFICIENT_ROLE,
      error.message,
      context,
    );
  }

  if (
    error.message?.includes("session") ||
    error.message?.includes("Session")
  ) {
    return createPermissionError(
      PermissionErrorType.INVALID_SESSION,
      "Invalid or expired session",
      context,
    );
  }

  if (
    error.message?.includes("organization") ||
    error.message?.includes("Organization")
  ) {
    return createPermissionError(
      PermissionErrorType.ORGANIZATION_ACCESS_DENIED,
      error.message,
      context,
    );
  }

  // Generic error
  return createPermissionError(
    PermissionErrorType.INSUFFICIENT_PERMISSIONS,
    error.message || "Access denied",
    context,
  );
}

/**
 * HTTP status code mapping for permission errors
 */
export function getHttpStatusForPermissionError(
  error: PermissionErrorResponse,
): number {
  switch (error.code) {
    case PermissionErrorType.SESSION_EXPIRED:
    case PermissionErrorType.INVALID_SESSION:
      return 401;
    case PermissionErrorType.ORGANIZATION_ACCESS_DENIED:
    case PermissionErrorType.INSUFFICIENT_PERMISSIONS:
    case PermissionErrorType.INSUFFICIENT_ROLE:
    case PermissionErrorType.INSUFFICIENT_PERMISSIONS_OR_ROLES:
      return 403;
    default:
      return 403;
  }
}

/**
 * Client-side error handler for permission errors
 */
export function handleClientPermissionError(error: PermissionErrorResponse): {
  title: string;
  message: string;
  action?: string;
} {
  switch (error.code) {
    case PermissionErrorType.SESSION_EXPIRED:
    case PermissionErrorType.INVALID_SESSION:
      return {
        title: "Session Expired",
        message: "Your session has expired. Please sign in again.",
        action: "Sign In",
      };

    case PermissionErrorType.INSUFFICIENT_PERMISSIONS:
      return {
        title: "Access Denied",
        message: error.message,
        action: "Contact Administrator",
      };

    case PermissionErrorType.INSUFFICIENT_ROLE:
      return {
        title: "Insufficient Role",
        message: "You do not have the required role to access this resource.",
        action: "Request Access",
      };

    case PermissionErrorType.ORGANIZATION_ACCESS_DENIED:
      return {
        title: "Organization Access Denied",
        message: "You do not have access to this organization.",
        action: "Switch Organization",
      };

    default:
      return {
        title: "Access Denied",
        message: "You do not have permission to access this resource.",
        action: "Contact Support",
      };
  }
}

/**
 * Log permission errors for monitoring
 */
export function logPermissionError(
  error: PermissionErrorResponse,
  context?: {
    userId?: string;
    path?: string;
    method?: string;
    userAgent?: string;
  },
): void {
  const logData = {
    error: error.code,
    message: error.message,
    details: error.details,
    context,
    timestamp: error.timestamp,
  };

  console.error("[PERMISSION ERROR]", JSON.stringify(logData, null, 2));

  // In production, you might want to send this to a logging service
  // Example: sendToLoggingService(logData);
}

/**
 * Permission error boundary component props
 */
export interface PermissionErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: (error: PermissionErrorResponse) => React.ReactNode;
  onError?: (error: PermissionErrorResponse) => void;
}

/**
 * Utility to check if an error is a permission error
 */
export function isPermissionError(
  error: any,
): error is PermissionErrorResponse {
  return (
    error &&
    typeof error === "object" &&
    "code" in error &&
    Object.values(PermissionErrorType).includes(error.code)
  );
}

/**
 * Create user-friendly error messages
 */
export function getUserFriendlyErrorMessage(
  error: PermissionErrorResponse,
): string {
  const clientError = handleClientPermissionError(error);
  return clientError.message;
}

/**
 * Get suggested actions for permission errors
 */
export function getSuggestedActions(error: PermissionErrorResponse): string[] {
  const actions: string[] = [];

  switch (error.code) {
    case PermissionErrorType.SESSION_EXPIRED:
    case PermissionErrorType.INVALID_SESSION:
      actions.push("Sign in again");
      break;

    case PermissionErrorType.INSUFFICIENT_PERMISSIONS:
      actions.push("Request additional permissions");
      actions.push("Contact your administrator");
      break;

    case PermissionErrorType.INSUFFICIENT_ROLE:
      actions.push("Request role upgrade");
      actions.push("Contact your administrator");
      break;

    case PermissionErrorType.ORGANIZATION_ACCESS_DENIED:
      actions.push("Switch to a different organization");
      actions.push("Request organization access");
      break;

    default:
      actions.push("Contact support");
      break;
  }

  return actions;
}
