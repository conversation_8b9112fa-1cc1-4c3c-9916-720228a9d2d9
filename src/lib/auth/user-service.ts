import { PermissionString } from "../../types/auth";

export interface UserProfile {
    id: string;
    email: string;
    name?: string;
    username: string;
    permissions: PermissionString[];
    roles: string[];
    organizations?: Array<{
        org: {
            role: string;
            email: string;
            organization: {
                name: string;
                _id: string;
            };
        };
    }>;
    currentOrganizationId?: string;
}

export interface UserServiceResult {
    success: boolean;
    user?: UserProfile;
    error?: string;
}

/**
 * Service for fetching user profile and permissions from external API
 */
export class UserService {
    private static EXTERNAL_API_URL =
        process.env.EXTERNAL_API_URL ||
        process.env.API_BASE_URL ||
        "https://api.qbraid.com";
    private static EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY;

    /**
     * Fetch user profile with permissions and roles
     */
    static async getUserProfile(
        userId: string,
        idToken?: string,
        organizationId?: string,
        email?: string
    ): Promise<UserServiceResult> {
        try {
            // For development: if no external API key and no id token, return mock data
            if (!idToken) {
                console.warn(
                    "No external API authentication available, using mock user profile"
                );
                return {
                    success: true,
                    user: {
                        id: userId,
                        email: email || `user-${userId}@example.com`,
                        name: email ? email.split("@")[0] : `User ${userId}`,
                        username: email || `user-${userId}`,
                        permissions: [
                            "global|org:*|users|read",
                            "org:*|projects|read",
                            "user:self|profile|read",
                        ],
                        roles: ["org_member"],
                        currentOrganizationId: undefined,
                    },
                };
            }

            // Build URL to new context endpoints using email + idtoken query
            if (!email) {
                console.warn(
                    "Email not provided to getUserProfile; falling back to legacy identifier path"
                );
            }

            const identifier = email || userId;
            let rawBase =
                process.env.EXTERNAL_API_URL ||
                process.env.API_BASE_URL ||
                "http://localhost:3001/api/v1";

            // Fix malformed URLs without protocol
            if (
                !rawBase.startsWith("http://") &&
                !rawBase.startsWith("https://")
            ) {
                rawBase = `http://${rawBase}`;
            }

            // Remove trailing slash and any existing /api/v1 path
            const baseRoot = rawBase
                .replace(/\/$/, "")
                .replace(/\/api\/v1$/, "");

            // Now construct the full path
            const base = `${baseRoot}/api/v1/users/${encodeURIComponent(
                identifier
            )}${organizationId ? `/organizations/${encodeURIComponent(organizationId)}` : ""}/context`;

            // Debug logging (safe)
            try {
                const tokenPreview = idToken
                    ? `${idToken.slice(0, 12)}...(${idToken.length})`
                    : null;
                console.log("[UserService] Requesting user context", {
                    url: base,
                    emailUsed: email,
                    identifier,
                    hasIdToken: !!idToken,
                    idTokenPreview: tokenPreview,
                    organizationId,
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: idToken
                            ? "Bearer <redacted>"
                            : undefined,
                        "X-Organization-ID": organizationId || undefined,
                    },
                });
            } catch {}

            const response = await fetch(base, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    ...(idToken ? { Authorization: `Bearer ${idToken}` } : {}),
                    ...(organizationId
                        ? { "X-Organization-ID": organizationId }
                        : {}),
                },
            });

            if (!response.ok) {
                try {
                    const text = await response.text();
                    console.warn("[UserService] Context API non-OK response", {
                        status: response.status,
                        statusText: response.statusText,
                        bodyPreview: text?.slice(0, 300),
                    });
                } catch {}
                return {
                    success: false,
                    error: `Failed to fetch user profile: ${response.status} ${response.statusText}`,
                };
            }

            const envelope = await response.json();
            const userData = envelope?.data ?? envelope;

            // Normalize organizations: API returns an object map keyed by orgId
            let normalizedRoles: string[] = [];
            let normalizedPermissions: PermissionString[] = [];
            try {
                const orgs = userData?.organizations;
                if (orgs && typeof orgs === "object" && !Array.isArray(orgs)) {
                    const rolesSet = new Set<string>();
                    const permsSet = new Set<PermissionString>();
                    for (const orgId of Object.keys(orgs)) {
                        const org = orgs[orgId] || {};
                        (org.roles || []).forEach((r: string) =>
                            rolesSet.add(r)
                        );
                        (org.customPermissions || []).forEach(
                            (p: PermissionString) => permsSet.add(p)
                        );
                    }
                    normalizedRoles = Array.from(rolesSet);
                    normalizedPermissions = Array.from(permsSet);
                }
            } catch {}

            try {
                console.log("[UserService] Context API success", {
                    hasDataWrapper: !!envelope?.data,
                    id: userData?.id || userData?.user?.id || userId,
                    email: userData?.email || userData?.user?.email || email,
                    orgs: Array.isArray(userData?.organizations)
                        ? userData.organizations.length
                        : undefined,
                    roles: Array.isArray(userData?.roles)
                        ? userData.roles.length
                        : undefined,
                    permissions: Array.isArray(userData?.permissions)
                        ? userData.permissions.length
                        : Array.isArray(userData?.effectivePermissions)
                          ? userData.effectivePermissions.length
                          : normalizedPermissions.length || undefined,
                });
            } catch {}

            // Transform the response to our UserProfile interface
            const payloadUser = userData.user || {};
            const userProfile: UserProfile = {
                id: userData.id || payloadUser.id || userId,
                email: userData.email || payloadUser.email || email || "",
                name: userData.name || payloadUser.name,
                username:
                    userData.username ||
                    payloadUser.userName ||
                    payloadUser.username ||
                    userData.email ||
                    email ||
                    "",
                permissions:
                    userData.permissions ||
                    userData.effectivePermissions ||
                    normalizedPermissions ||
                    [],
                roles: userData.roles || normalizedRoles || [],
                organizations: userData.organizations,
                currentOrganizationId:
                    userData.currentOrganizationId ||
                    userData.organizationId ||
                    organizationId,
            };

            return {
                success: true,
                user: userProfile,
            };
        } catch (error) {
            console.error("[UserService] User profile fetch error", {
                message: error instanceof Error ? error.message : String(error),
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    /**
     * Fetch user permissions and roles for a specific organization
     */
    static async getUserPermissionsForOrg(
        userId: string,
        organizationId: string,
        idToken?: string
    ): Promise<{ permissions: PermissionString[]; roles: string[] } | null> {
        try {
            const result = await this.getUserProfile(
                userId,
                idToken,
                organizationId
            );

            if (!result.success || !result.user) {
                return null;
            }

            return {
                permissions: result.user.permissions,
                roles: result.user.roles,
            };
        } catch (error) {
            console.error("User permissions fetch error:", error);
            return null;
        }
    }

    /**
     * Get user's default organization
     */
    static async getUserDefaultOrganization(
        userId: string,
        idToken?: string
    ): Promise<string | null> {
        try {
            const result = await this.getUserProfile(userId, idToken);

            if (!result.success || !result.user) {
                return null;
            }

            return result.user.currentOrganizationId || null;
        } catch (error) {
            console.error("User default organization fetch error:", error);
            return null;
        }
    }

    /**
     * Validate if user has access to an organization
     */
    static async validateOrganizationAccess(
        userId: string,
        organizationId: string,
        idToken?: string
    ): Promise<boolean> {
        try {
            const result = await this.getUserProfile(
                userId,
                idToken,
                organizationId
            );
            return result.success && !!result.user;
        } catch (error) {
            console.error("Organization access validation error:", error);
            return false;
        }
    }
}
