// lib/session.ts

"use server";

import { JWTPayload, SignJWT, jwtVerify } from "jose";
import { cookies } from "next/headers";
import {
    SessionPayload,
    CSRFToken,
    UserSessionData,
    CognitoTokens,
    PermissionString,
} from "@/types/auth";
import {
    SESSION_SECRET,
    SESSION_COOKIE_NAME,
    CSRF_COOKIE_NAME,
    ACCESS_TOKEN_COOKIE_NAME,
    ID_TOKEN_COOKIE_NAME,
    SESSION_DURATION_SECONDS,
    SESSION_DURATION_MS,
    CSRF_TOKEN_LENGTH,
    REDIS_SESSION_PREFIX,
    REDIS_COGNITO_PREFIX,
    STORAGE_MODE,
    REDIS_HEALTH_CACHE_TTL,
    SESSION_ID_PATTERN,
    JWT_PATTERN,
    CSRF_TOKEN_EXPIRY_MS,
    TOKEN_COOKIE_MAX_AGE,
    SESSION_REFRESH_THRESHOLD,
    CLEANUP_BATCH_SIZE,
} from "@/lib/utils/constant";
import { isDevelopment } from "@/lib/utils/dev-utils";

// Cache variables
let redisClientCache: any = null;
const redisHealthCache: { healthy: boolean; lastCheck: number } = {
    healthy: false,
    lastCheck: 0,
};
let isEdgeCache: boolean | null = null;

/**
 * Optimized secure cookie configuration with memoization
 */
const cookieOptionsCache = new Map<number, any>();
const getSecureCookieOptions = (maxAge: number = SESSION_DURATION_SECONDS) => {
    if (cookieOptionsCache.has(maxAge)) {
        return cookieOptionsCache.get(maxAge);
    }

    const options = {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict" as const, // Changed from 'strict' to 'lax' for better compatibility
        maxAge,
        path: "/",
        // In development, ensure cookies work with localhost
        ...(process.env.NODE_ENV === "development" && {
            domain: "localhost",
        }),
        ...(process.env.NODE_ENV === "production" && {
            domain: process.env.COOKIE_DOMAIN,
        }),
    };

    cookieOptionsCache.set(maxAge, options);
    return options;
};

/**
 * Optimized token generation with configurable length
 */
const generateSecureToken = (length: number = CSRF_TOKEN_LENGTH): string => {
    const array = new Uint8Array(Math.ceil(length / 2));
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0"))
        .join("")
        .slice(0, length);
};

/**
 * Cached edge runtime detection
 */
const isEdgeRuntime = (): boolean => {
    if (isEdgeCache !== null) return isEdgeCache;

    try {
        isEdgeCache =
            typeof process === "undefined" ||
            (typeof globalThis !== "undefined" && "EdgeRuntime" in globalThis);
    } catch {
        isEdgeCache = true;
    }

    return isEdgeCache;
};

/**
 * Optimized Redis availability check with caching
 */
async function shouldUseRedisStorage(): Promise<boolean> {
    if (STORAGE_MODE === "cookies" || isEdgeRuntime()) {
        return false;
    }

    const now = Date.now();
    if (now - redisHealthCache.lastCheck < REDIS_HEALTH_CACHE_TTL) {
        return redisHealthCache.healthy;
    }

    try {
        if (redisClientCache) {
            // Use cached client for health check
            const redis = redisClientCache();
            await redis.ping();
            redisHealthCache.healthy = true;
        } else {
            const { getRedisClient, isRedisHealthy } = await import(
                "../redis/redis"
            );
            redisClientCache = getRedisClient;
            redisHealthCache.healthy = await isRedisHealthy();
        }

        redisHealthCache.lastCheck = now;
        return redisHealthCache.healthy;
    } catch {
        redisHealthCache.healthy = false;
        redisHealthCache.lastCheck = now;
        return false;
    }
}

/**
 * Optimized Redis operations with connection reuse
 */
async function getRedisClient() {
    if (!redisClientCache) {
        const { getRedisClient } = await import("../redis/redis");
        redisClientCache = getRedisClient;
    }
    return redisClientCache();
}

const RedisOperations = {
    async setData(
        key: string,
        data: any,
        ttlSeconds: number
    ): Promise<boolean> {
        try {
            const redis = await getRedisClient();
            const serializedData = JSON.stringify(data);
            await redis.setex(key, ttlSeconds, serializedData);
            return true;
        } catch {
            // Failed to store in Redis
            return false;
        }
    },

    async getData(key: string): Promise<any | null> {
        try {
            const redis = await getRedisClient();
            const data = await redis.get(key);
            return data ? JSON.parse(data) : null;
        } catch {
            // Failed to get from Redis
            return null;
        }
    },

    async deleteData(key: string): Promise<boolean> {
        try {
            const redis = await getRedisClient();
            await redis.del(key);
            return true;
        } catch {
            // Failed to delete from Redis
            return false;
        }
    },

    async batchDelete(keys: string[]): Promise<void> {
        if (keys.length === 0) return;

        try {
            const redis = await getRedisClient();
            await redis.del(...keys);
        } catch {
            // Redis batch delete failed
        }
    },
};

/**
 * Optimized session creation with better error handling
 */
export async function createSession(
    userData: UserSessionData,
    tokens?: { idToken?: string; accessToken?: string },
    userContext?: any
): Promise<string> {
    const now = Date.now();
    const sessionId = generateSecureToken(16);

    const sessionData: SessionPayload = {
        ...userData,
        email: userData.email,
        permissions: userData.permissions || [],
        roles: userData.roles || [],
        organizationId: userData.organizationId,
        signedIn: true,
        iat: Math.floor(now / 1000),
        exp: Math.floor((now + SESSION_DURATION_MS) / 1000),
        jti: sessionId,
    };

    // Try Redis first
    if (await shouldUseRedisStorage()) {
        try {
            // 1. Store main session data
            const sessionStored = await RedisOperations.setData(
                `${REDIS_SESSION_PREFIX}${sessionId}`,
                sessionData,
                SESSION_DURATION_SECONDS
            );

            if (sessionStored) {
                // 2. Store Cognito tokens separately under session:{id}:cognito
                if (tokens?.idToken || tokens?.accessToken) {
                    await RedisOperations.setData(
                        `session:${sessionId}:cognito`,
                        {
                            idToken: tokens.idToken,
                            accessToken: tokens.accessToken,
                            storedAt: new Date().toISOString(),
                        },
                        SESSION_DURATION_SECONDS
                    );
                }

                // 3. Store user context under user:{email}:context
                if (userContext) {
                    await RedisOperations.setData(
                        `user:${userData.email}:context`,
                        {
                            ...userContext,
                            lastUpdated: new Date().toISOString(),
                            sessionId: sessionId,
                        },
                        SESSION_DURATION_SECONDS
                    );
                }

                // Redis session created
                return sessionId;
            }
        } catch (error) {
            console.error("[SESSION] Redis storage error:", error);
        }
        // Redis storage failed, falling back to JWT
    }

    // Fallback to JWT
    try {
        const jwt = await new SignJWT(sessionData as unknown as JWTPayload)
            .setProtectedHeader({ alg: "HS256" })
            .setIssuedAt()
            .setExpirationTime(sessionData.exp)
            .setJti(sessionId)
            .sign(SESSION_SECRET);

        // JWT session created
        return jwt;
    } catch {
        // JWT creation failed
        throw new Error("Failed to create session");
    }
}

/**
 * Optimized session verification with pattern matching
 */
export async function verifySession(
    token: string
): Promise<SessionPayload | null> {
    if (!token) return null;

    const isSessionId = SESSION_ID_PATTERN.test(token);
    const isJWT = JWT_PATTERN.test(token);

    // Handle Redis session ID
    if (isSessionId && (await shouldUseRedisStorage())) {
        const sessionData = await RedisOperations.getData(
            `${REDIS_SESSION_PREFIX}${token}`
        );

        if (!sessionData) return null;

        // Check expiration
        if (sessionData.exp && Date.now() / 1000 > sessionData.exp) {
            RedisOperations.deleteData(`${REDIS_SESSION_PREFIX}${token}`); // Fire and forget
            return null;
        }

        return sessionData as SessionPayload;
    }

    // Handle JWT
    if (isJWT) {
        try {
            const { payload } = await jwtVerify(token, SESSION_SECRET);

            // Validate required fields
            const requiredFields = ["username", "email", "signedIn", "jti"];
            if (!requiredFields.every((field) => field in payload)) {
                return null;
            }

            return payload as unknown as SessionPayload;
        } catch {
            // JWT verification failed
            return null;
        }
    }

    // For security, do not trust raw session-id format outside Redis/JWT.

    // Invalid token format detected
    return null;
}

/**
 * Optimized session retrieval with error handling
 */
export async function getSession(): Promise<SessionPayload | null> {
    try {
        const cookieStore = await cookies();
        const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);

        if (process.env.NODE_ENV === "development") {
            console.log("🔍 [SESSION] Retrieving session:", {
                cookieName: SESSION_COOKIE_NAME,
                hasCookie: !!sessionCookie,
                cookieValue: sessionCookie?.value
                    ? sessionCookie.value.slice(0, 8) + "..."
                    : null,
            });
        }

        if (!sessionCookie?.value) {
            if (process.env.NODE_ENV === "development") {
                console.log("❌ [SESSION] No session cookie found");
            }
            return null;
        }

        const session = await verifySession(sessionCookie.value);

        if (!session && sessionCookie.value) {
            // Clean up invalid session cookie
            if (process.env.NODE_ENV === "development") {
                console.log("🗑️ [SESSION] Invalid session, cleaning up cookie");
            }
            cookieStore.delete(SESSION_COOKIE_NAME);
        }

        if (process.env.NODE_ENV === "development" && session) {
            console.log("✅ [SESSION] Session retrieved successfully:", {
                email: session.email,
                sessionId: session.jti?.slice(0, 8) + "...",
            });
        }

        return session;
    } catch (error) {
        // Session retrieval error
        if (isDevelopment) {
            console.error("❌ [SESSION] Session retrieval failed:", error);
        }
        return null;
    }
}

/**
 * Middleware-optimized session verification
 */
export const verifySessionForMiddleware = verifySession; // Use same optimized function

/**
 * Optimized session cookie setting
 */
export async function setSessionCookie(sessionToken: string): Promise<void> {
    const cookieStore = await cookies();
    const options = getSecureCookieOptions();

    let cookieValue = sessionToken;
    try {
        if (
            SESSION_ID_PATTERN.test(sessionToken) &&
            (await shouldUseRedisStorage())
        ) {
            const sessionData = await RedisOperations.getData(
                `${REDIS_SESSION_PREFIX}${sessionToken}`
            );
            if (sessionData) {
                const jwt = await new SignJWT(
                    sessionData as unknown as JWTPayload
                )
                    .setProtectedHeader({ alg: "HS256" })
                    .setIssuedAt()
                    .setExpirationTime(sessionData.exp)
                    .setJti(sessionData.jti)
                    .sign(SESSION_SECRET);
                cookieValue = jwt;
            }
        }
    } catch {}

    if (process.env.NODE_ENV === "development") {
        console.log("🍪 [SESSION] Setting session cookie:", {
            name: SESSION_COOKIE_NAME,
            value: cookieValue.slice(0, 8) + "...",
            options: {
                ...options,
                secure: options.secure,
                sameSite: options.sameSite,
                domain: options.domain,
            },
        });
    }

    cookieStore.set(SESSION_COOKIE_NAME, cookieValue, options);
    // Session cookie set
}

/**
 * Optimized CSRF token generation and verification
 */
export async function generateCSRFToken(): Promise<string> {
    const token = generateSecureToken();
    const csrfData: CSRFToken = {
        token,
        exp: Date.now() + CSRF_TOKEN_EXPIRY_MS,
    };

    const cookieStore = await cookies();
    cookieStore.set(CSRF_COOKIE_NAME, JSON.stringify(csrfData), {
        ...getSecureCookieOptions(TOKEN_COOKIE_MAX_AGE),
        httpOnly: true,
    });
    return token;
}

export async function verifyCSRFToken(providedToken: string): Promise<boolean> {
    if (!providedToken) return false;

    try {
        const cookieStore = await cookies();
        const csrfCookie = cookieStore.get(CSRF_COOKIE_NAME);

        if (!csrfCookie?.value) return false;

        const csrfData: CSRFToken = JSON.parse(csrfCookie.value);
        return csrfData.token === providedToken && Date.now() < csrfData.exp;
    } catch {
        // CSRF verification failed
        return false;
    }
}

/**
 * Optimized session clearing with batch operations
 */
export async function clearSession(sessionId?: string): Promise<void> {
    // Clearing session data

    const cookieStore = await cookies();

    // Get session ID from cookie if not provided
    if (!sessionId) {
        const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);
        sessionId = sessionCookie?.value;
    }

    // Batch Redis cleanup
    if (sessionId && (await shouldUseRedisStorage())) {
        const keysToDelete = [
            `${REDIS_SESSION_PREFIX}${sessionId}`,
            `${REDIS_COGNITO_PREFIX}${sessionId}`,
        ];
        await RedisOperations.batchDelete(keysToDelete);
    }

    // Base options for deleting cookies, matching how they were set
    // For __Secure- and __Host- prefixed cookies, attributes must match.
    const baseDeleteOptions = {
        path: "/",
        secure: process.env.NODE_ENV === "production",
        ...(process.env.NODE_ENV === "production" && {
            domain: process.env.COOKIE_DOMAIN,
        }),
    };

    // Batch cookie cleanup
    const cookiesToClear = [
        SESSION_COOKIE_NAME,
        CSRF_COOKIE_NAME,
        ACCESS_TOKEN_COOKIE_NAME,
        ID_TOKEN_COOKIE_NAME,
        "csrf-token", // Fallback for dev if CSRF_COOKIE_NAME wasn't used
    ];

    // Clear standard cookies with explicit options
    for (const name of cookiesToClear) {
        try {
            cookieStore.delete({ name, ...baseDeleteOptions });
        } catch {
            // Failed to delete cookie
            // Fallback to simple delete if specific options fail
            try {
                cookieStore.delete({ name });
            } catch {
                // Fallback delete also failed for cookie
            }
        }
    }

    // Clear Cognito SDK cookies
    const allCookies = cookieStore.getAll();
    const cognitoCookies = allCookies.filter((cookie) =>
        cookie.name.startsWith("CognitoIdentityServiceProvider")
    );

    for (const cognitoCookie of cognitoCookies) {
        try {
            // Cognito cookies might have different paths/domains.
            // Try deleting with common attributes first.
            cookieStore.delete({
                name: cognitoCookie.name,
                path: "/",
                secure: baseDeleteOptions.secure,
            });
            if (
                process.env.NODE_ENV === "production" &&
                baseDeleteOptions.domain
            ) {
                // Attempt deletion with domain as well for production, if domain is set
                cookieStore.delete({
                    name: cognitoCookie.name,
                    path: "/",
                    secure: true,
                    domain: baseDeleteOptions.domain,
                });
            }
        } catch {
            // Failed to delete Cognito cookie
            // Fallback to simple delete
            try {
                cookieStore.delete({ name: cognitoCookie.name });
            } catch {
                // Fallback delete also failed for Cognito cookie
            }
        }
    }

    // Session cleanup completed
}

/**
 * Optimized session refresh with threshold checking
 */
export async function refreshSession(
    currentSession: SessionPayload
): Promise<string | null> {
    const timeUntilExpiry = currentSession.exp * 1000 - Date.now();
    const refreshThreshold = SESSION_DURATION_MS * SESSION_REFRESH_THRESHOLD;

    if (timeUntilExpiry >= refreshThreshold) {
        return null; // No refresh needed
    }

    try {
        return await createSession({
            username: currentSession.username,
            email: currentSession.email.toLowerCase(),
            userId: currentSession.userId,
        });
    } catch {
        // Session refresh failed
        return null;
    }
}

/**
 * Combined session validation and refresh
 */
export async function validateAndRefreshSession(): Promise<SessionPayload | null> {
    const session = await getSession();
    if (!session) return null;

    const newToken = await refreshSession(session);
    if (newToken) {
        await setSessionCookie(newToken);
        return await verifySession(newToken);
    }

    return session;
}

/**
 * Optimized Cognito token management
 */
export async function setCognitoTokenCookies(
    tokens: CognitoTokens,
    sessionId?: string
): Promise<void> {
    // Try Redis first (preferred method)
    if (sessionId && (await shouldUseRedisStorage())) {
        // Use new structure: session:{id}:cognito
        const success = await RedisOperations.setData(
            `session:${sessionId}:cognito`,
            {
                idToken: tokens.idToken,
                accessToken: tokens.accessToken,
                storedAt: new Date().toISOString(),
            },
            SESSION_DURATION_SECONDS
        );
        if (success) {
            // Token Redis storage successful
            console.log("🔐 [SESSION] Cognito tokens stored securely in Redis");
            return;
        } else {
            console.warn(
                "⚠️ [SESSION] Failed to store Cognito tokens in Redis, falling back to cookies"
            );
        }
    } else {
        console.warn(
            "⚠️ [SESSION] Redis not available, falling back to cookies"
        );
    }

    // Secure fallback to cookies if Redis is unavailable
    try {
        const cookieStore = await cookies();
        const tokenCookieOptions = getSecureCookieOptions(TOKEN_COOKIE_MAX_AGE);

        if (tokens.accessToken) {
            cookieStore.set(
                ACCESS_TOKEN_COOKIE_NAME,
                tokens.accessToken,
                tokenCookieOptions
            );
        }
        if (tokens.idToken) {
            cookieStore.set(
                ID_TOKEN_COOKIE_NAME,
                tokens.idToken,
                tokenCookieOptions
            );
        }
        console.log(
            "🍪 [SESSION] Cognito tokens stored in cookies (fallback mode)"
        );
    } catch (error) {
        console.error(
            "❌ [SESSION] Failed to store tokens in cookies fallback:",
            error
        );
    }

    // Token cookie storage completed
}

/**
 * Get Cognito tokens from Redis using session ID
 */
export async function getCognitoTokensBySessionId(
    sessionId: string
): Promise<CognitoTokens | null> {
    try {
        if (!sessionId || !(await shouldUseRedisStorage())) {
            return null;
        }

        // Get tokens from session:{id}:cognito
        const tokens = await RedisOperations.getData(`session:${sessionId}:cognito`);

        if (tokens?.idToken || tokens?.accessToken) {
            return {
                idToken: tokens.idToken || "",
                accessToken: tokens.accessToken || "",
            };
        }

        return null;
    } catch (error) {
        console.error("[SESSION] Error retrieving tokens:", error);
        return null;
    }
}

/**
 * Get user context from Redis by email
 */
export async function getUserContextByEmail(
    email: string
): Promise<any | null> {
    try {
        if (!email || !(await shouldUseRedisStorage())) {
            return null;
        }

        const context = await RedisOperations.getData(
            `user:${email}:context`
        );

        return context || null;
    } catch (error) {
        console.error("[SESSION] Error retrieving user context:", error);
        return null;
    }
}

export async function getCognitoTokenCookies(
    sessionId?: string
): Promise<CognitoTokens> {
    // Try Redis first (preferred method)
    if (sessionId && (await shouldUseRedisStorage())) {
        const tokens = await RedisOperations.getData(
            `${REDIS_COGNITO_PREFIX}${sessionId}`
        );
        if (tokens) {
            console.log("🔐 [SESSION] Cognito tokens retrieved from Redis");
            return tokens as CognitoTokens;
        } else {
            console.warn(
                "⚠️ [SESSION] Tokens not found in Redis, checking cookies"
            );
        }
    } else {
        console.warn("⚠️ [SESSION] Redis not available, checking cookies");
    }

    // Secure fallback to cookies if Redis is unavailable
    try {
        const cookieStore = await cookies();
        const tokens = {
            accessToken: cookieStore.get(ACCESS_TOKEN_COOKIE_NAME)?.value,
            idToken: cookieStore.get(ID_TOKEN_COOKIE_NAME)?.value,
        };

        if (tokens.accessToken || tokens.idToken) {
            console.log(
                "🍪 [SESSION] Cognito tokens retrieved from cookies (fallback mode)"
            );
            return tokens;
        }
    } catch (error) {
        console.warn(
            "⚠️ [SESSION] Failed to retrieve tokens from cookies:",
            error
        );
    }

    // Return empty object if no tokens found
    return {};
}

export async function clearCognitoTokenCookies(): Promise<void> {
    // Clear from Redis first (preferred method)
    try {
        const currentSession = await getSession();
        if (currentSession?.jti) {
            await RedisOperations.deleteData(
                `${REDIS_COGNITO_PREFIX}${currentSession.jti}`
            );
            console.log("🗑️ [SESSION] Cognito tokens cleared from Redis");
        }
    } catch (error) {
        console.warn(
            "⚠️ [SESSION] Failed to clear Cognito tokens from Redis:",
            error
        );
    }

    // Also clear cookies as fallback cleanup
    try {
        const cookieStore = await cookies();
        cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
        cookieStore.delete(ID_TOKEN_COOKIE_NAME);
        console.log("🗑️ [SESSION] Cognito token cookies cleared");
    } catch (error) {
        console.warn("⚠️ [SESSION] Failed to clear token cookies:", error);
    }
}

/**
 * Utility functions
 */
export async function getCurrentSessionId(): Promise<string | null> {
    try {
        const cookieStore = await cookies();
        return cookieStore.get(SESSION_COOKIE_NAME)?.value || null;
    } catch {
        // Failed to get session ID
        return null;
    }
}

export async function getUserEmailFromToken(): Promise<string | null> {
    try {
        const sessionId = await getCurrentSessionId();
        if (!sessionId) return null;

        const tokens = await getCognitoTokenCookies(sessionId);
        if (!tokens.idToken) return null;
        if (!tokens.idToken?.split(".")[1]) return null;
        const payload = JSON.parse(atob(tokens.idToken?.split(".")[1] || ""));
        return payload.email || null;
    } catch {
        // Token email extraction failed
        return null;
    }
}

/**
 * Optimized cleanup with batch operations and improved filtering
 */
export async function cleanupOrphanedSessions(
    userEmail: string,
    currentSessionId?: string
): Promise<void> {
    if (!(await shouldUseRedisStorage())) return;

    try {
        const redis = await getRedisClient();
        const sessionKeys = await redis.keys(`${REDIS_SESSION_PREFIX}*`);

        if (sessionKeys.length === 0) return;

        const keysToDelete: string[] = [];
        const now = Date.now() / 1000;

        // Process sessions in batches
        for (let i = 0; i < sessionKeys.length; i += CLEANUP_BATCH_SIZE) {
            const batch = sessionKeys.slice(i, i + CLEANUP_BATCH_SIZE);

            await Promise.all(
                batch.map(async (key: string) => {
                    try {
                        const sessionData = await RedisOperations.getData(key);
                        if (!sessionData || sessionData.email !== userEmail)
                            return;

                        const sessionId = key.replace(REDIS_SESSION_PREFIX, "");
                        if (currentSessionId === sessionId) return;

                        // Mark expired sessions for deletion
                        if (sessionData.exp && now > sessionData.exp) {
                            keysToDelete.push(
                                key,
                                `${REDIS_COGNITO_PREFIX}${sessionId}`
                            );
                        }
                    } catch {
                        // Cleanup error processing key
                    }
                })
            );
        }

        // Batch delete expired sessions
        if (keysToDelete.length > 0) {
            await RedisOperations.batchDelete(keysToDelete);
            // Expired sessions cleanup completed
        }
    } catch {
        // Cleanup operation failed
    }
}

/**
 * Update session permissions and roles
 */
export async function updateSessionPermissions(
    sessionId: string,
    permissions: PermissionString[],
    roles: string[],
    organizationId?: string
): Promise<boolean> {
    try {
        const session = await verifySession(sessionId);
        if (!session) return false;

        const updatedSessionData: SessionPayload = {
            ...session,
            permissions,
            roles,
            organizationId,
        };

        // Update in Redis if available
        if (await shouldUseRedisStorage()) {
            const success = await RedisOperations.setData(
                `${REDIS_SESSION_PREFIX}${sessionId}`,
                updatedSessionData,
                SESSION_DURATION_SECONDS
            );
            if (success) return true;
        }

        // Fallback: create new JWT with updated data
        const newToken = await createSession({
            username: session.username,
            email: session.email,
            userId: session.userId,
            permissions,
            roles,
            organizationId,
        });

        if (newToken) {
            await setSessionCookie(newToken);
            return true;
        }

        return false;
    } catch {
        return false;
    }
}

/**
 * Switch organization context in session
 */
export async function switchOrganization(
    sessionId: string,
    organizationId: string,
    permissions: PermissionString[],
    roles: string[]
): Promise<boolean> {
    return await updateSessionPermissions(
        sessionId,
        permissions,
        roles,
        organizationId
    );
}

/**
 * Get current user's permissions from session
 */
export async function getSessionPermissions(): Promise<{
    permissions: PermissionString[];
    roles: string[];
    organizationId?: string;
} | null> {
    try {
        const session = await getSession();
        if (!session) return null;

        return {
            permissions: session.permissions || [],
            roles: session.roles || [],
            organizationId: session.organizationId,
        };
    } catch {
        return null;
    }
}

/**
 * Check if current session has specific permission
 */
export async function hasSessionPermission(
    requiredPermission: string
): Promise<boolean> {
    try {
        const sessionData = await getSessionPermissions();
        if (!sessionData) return false;

        const { hasPermission } = await import("../permissions/permissions");
        return hasPermission(sessionData.permissions, requiredPermission);
    } catch {
        return false;
    }
}

/**
 * Check if current session has specific role
 */
export async function hasSessionRole(requiredRole: string): Promise<boolean> {
    try {
        const sessionData = await getSessionPermissions();
        if (!sessionData) return false;

        return sessionData.roles.includes(requiredRole);
    } catch {
        return false;
    }
}
