import { SessionPayload } from "@/types/auth";

export interface AuthLogEvent {
  id: string;
  timestamp: Date;
  eventType: string;
  userId?: string;
  email?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  success: boolean;
  error?: string;
}

export class AuthLogger {
  private static instance: AuthLogger;
  private logs: AuthLogEvent[] = [];

  static getInstance(): AuthLogger {
    if (!AuthLogger.instance) {
      AuthLogger.instance = new AuthLogger();
    }
    return AuthLogger.instance;
  }

  private generateLogId(): string {
    return `auth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getClientInfo(request?: Request): {
    ipAddress?: string;
    userAgent?: string;
  } {
    if (!request) return {};

    const forwarded = request.headers.get("x-forwarded-for");
    const realIp = request.headers.get("x-real-ip");

    return {
      ipAddress: forwarded?.split(",")[0] || realIp || "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
    };
  }

  async logEvent(
    eventType: string,
    data: {
      userId?: string;
      email?: string;
      session?: SessionPayload;
      request?: Request;
      metadata?: Record<string, any>;
      success: boolean;
      error?: string;
    },
  ) {
    const { userId, email, session, request, metadata, success, error } = data;
    const clientInfo = this.getClientInfo(request);

    const logEvent: AuthLogEvent = {
      id: this.generateLogId(),
      timestamp: new Date(),
      eventType,
      userId: userId || session?.userId,
      email: email || session?.email,
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      sessionId: session?.jti,
      metadata,
      success,
      error,
    };

    // Add to in-memory logs (in production, this would go to a logging service)
    this.logs.push(logEvent);

    // Keep only last 1000 logs in memory
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }

    // Log to console in development
    if (process.env.NODE_ENV === "development") {
      console.log("[Auth Event]", JSON.stringify(logEvent, null, 2));
    }

    // Send to external monitoring service if configured
    if (process.env.AUTH_MONITORING_WEBHOOK) {
      this.sendToMonitoringService(logEvent);
    }

    // Send to analytics if configured
    if (process.env.AUTH_ANALYTICS_WEBHOOK) {
      this.sendToAnalytics(logEvent);
    }
  }

  private async sendToMonitoringService(event: AuthLogEvent) {
    try {
      await fetch(process.env.AUTH_MONITORING_WEBHOOK!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.AUTH_MONITORING_TOKEN}`,
        },
        body: JSON.stringify(event),
      });
    } catch (error) {
      console.error("Failed to send auth event to monitoring service:", error);
    }
  }

  private async sendToAnalytics(event: AuthLogEvent) {
    // Strip sensitive data for analytics
    const analyticsEvent = {
      eventType: event.eventType,
      timestamp: event.timestamp,
      success: event.success,
      userAgent: event.userAgent,
      metadata: event.metadata,
    };

    try {
      await fetch(process.env.AUTH_ANALYTICS_WEBHOOK!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(analyticsEvent),
      });
    } catch (error) {
      console.error("Failed to send auth event to analytics:", error);
    }
  }

  // Convenience methods for specific events
  async logSignInAttempt(
    email: string,
    success: boolean,
    request?: Request,
    error?: string,
    metadata?: Record<string, any>,
  ) {
    await this.logEvent("SIGN_IN_ATTEMPT", {
      email,
      success,
      request,
      error,
      metadata,
    });
  }

  async logSignInSuccess(
    userId: string,
    email: string,
    session?: SessionPayload,
    request?: Request,
    metadata?: Record<string, any>,
  ) {
    await this.logEvent("SIGN_IN_SUCCESS", {
      userId,
      email,
      session,
      request,
      success: true,
      metadata,
    });
  }

  async logSignInFailure(
    email: string,
    error: string,
    request?: Request,
    metadata?: Record<string, any>,
  ) {
    await this.logEvent("SIGN_IN_FAILURE", {
      email,
      success: false,
      error,
      request,
      metadata,
    });
  }

  async logSignOut(
    userId: string,
    email: string,
    session: SessionPayload,
    request?: Request,
  ) {
    await this.logEvent("SIGN_OUT", {
      userId,
      email,
      session,
      request,
      success: true,
    });
  }

  async logSignUpAttempt(
    email: string,
    success: boolean,
    request?: Request,
    error?: string,
  ) {
    await this.logEvent("SIGN_UP_ATTEMPT", {
      email,
      success,
      request,
      error,
    });
  }

  async logPasswordResetRequest(
    email: string,
    success: boolean,
    request?: Request,
    error?: string,
  ) {
    await this.logEvent("PASSWORD_RESET_REQUEST", {
      email,
      success,
      request,
      error,
    });
  }

  async logPasswordResetSuccess(email: string, request?: Request) {
    await this.logEvent("PASSWORD_RESET_SUCCESS", {
      email,
      success: true,
      request,
    });
  }

  async logSessionValidation(
    sessionId: string,
    success: boolean,
    request?: Request,
    error?: string,
  ) {
    await this.logEvent("SESSION_VALIDATION", {
      metadata: { sessionId },
      success,
      request,
      error,
    });
  }

  async logSuspiciousActivity(
    eventType: string,
    email?: string,
    request?: Request,
    metadata?: Record<string, any>,
  ) {
    await this.logEvent(`SUSPICIOUS_${eventType}`, {
      email,
      success: false,
      request,
      metadata: {
        severity: "high",
        ...metadata,
      },
    });
  }

  // Get recent logs for debugging
  getRecentLogs(count: number = 100): AuthLogEvent[] {
    return this.logs.slice(-count);
  }

  // Get logs for a specific user
  getUserLogs(userId: string): AuthLogEvent[] {
    return this.logs.filter((log) => log.userId === userId);
  }

  // Get failure rate for a time period
  getFailureRate(hours: number = 24): number {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentLogs = this.logs.filter((log) => log.timestamp > cutoff);

    if (recentLogs.length === 0) return 0;

    const failures = recentLogs.filter((log) => !log.success);
    return (failures.length / recentLogs.length) * 100;
  }

  // Detect suspicious patterns
  detectSuspiciousActivity(): {
    multipleFailedAttempts: AuthLogEvent[];
    unusualIPs: string[];
    unusualUserAgents: string[];
  } {
    const recentLogs = this.getRecentLogs(1000);
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    // Multiple failed attempts from same IP
    const ipFailureCounts = new Map<string, number>();
    const multipleFailedAttempts: AuthLogEvent[] = [];

    recentLogs.forEach((log) => {
      if (log.timestamp > oneHourAgo && !log.success && log.ipAddress) {
        const count = ipFailureCounts.get(log.ipAddress) || 0;
        ipFailureCounts.set(log.ipAddress, count + 1);

        if (count >= 5) {
          multipleFailedAttempts.push(log);
        }
      }
    });

    // Unusual IPs (not seen before)
    const allIPs = new Set(
      recentLogs
        .map((log) => log.ipAddress)
        .filter((ip): ip is string => ip !== undefined),
    );
    const recentIPs = new Set(
      recentLogs
        .filter((log) => log.timestamp > oneHourAgo)
        .map((log) => log.ipAddress)
        .filter((ip): ip is string => ip !== undefined),
    );

    const unusualIPs = Array.from(recentIPs).filter((ip) => !allIPs.has(ip));

    // Unusual user agents
    const allUserAgents = new Set(
      recentLogs
        .map((log) => log.userAgent)
        .filter((ua): ua is string => ua !== undefined),
    );
    const recentUserAgents = new Set(
      recentLogs
        .filter((log) => log.timestamp > oneHourAgo)
        .map((log) => log.userAgent)
        .filter((ua): ua is string => ua !== undefined),
    );

    const unusualUserAgents = Array.from(recentUserAgents).filter(
      (ua) => !allUserAgents.has(ua),
    );

    return {
      multipleFailedAttempts,
      unusualIPs,
      unusualUserAgents,
    };
  }
}

export const authLogger = AuthLogger.getInstance();
