import { NextRequest } from "next/server";
import {
    getSession,
    getCognitoTokensBySessionId,
    getUserContextByEmail,
} from "./session";

export interface SessionContext {
    sessionId: string;
    userId: string;
    email: string;
    idToken: string;
    accessToken: string;
    permissions: string[];
    roles: string[];
    organizationId?: string;
    userContext?: any;
}

/**
 * Middleware to extract session information and tokens from request
 * Clients send session ID, we convert it to tokens for external API calls
 */
export async function getSessionContext(
    request: NextRequest
): Promise<SessionContext | null> {
    try {
        // 1. Get session from cookie
        const session = await getSession();
        if (!session?.signedIn || !session.jti) {
            return null;
        }

        // 2. Get Cognito tokens using session ID
        const tokens = await getCognitoTokensBySessionId(session.jti);
        if (!tokens?.idToken) {
            console.error("[SESSION-MIDDLEWARE] No ID token found for session");
            return null;
        }

        // 3. Get user context from Redis (optional, for caching)
        const userContext = await getUserContextByEmail(session.email);

        return {
            sessionId: session.jti,
            userId: session.userId,
            email: session.email,
            idToken: tokens.idToken,
            accessToken: tokens.accessToken || "",
            permissions: session.permissions || [],
            roles: session.roles || [],
            organizationId: session.organizationId,
            userContext: userContext,
        };
    } catch (error) {
        console.error(
            "[SESSION-MIDDLEWARE] Error getting session context:",
            error
        );
        return null;
    }
}

/**
 * Helper to get ID token from session for external API calls
 */
export async function getIdTokenFromSession(): Promise<string | null> {
    const context = await getSessionContext({} as NextRequest);
    return context?.idToken || null;
}

/**
 * Extract organization ID from request headers or session
 */
export function getOrganizationId(
    request: NextRequest,
    sessionContext: SessionContext
): string | undefined {
    // Priority: Header > Session
    const headerOrgId = request.headers.get("x-organization-id");
    return headerOrgId || sessionContext.organizationId;
}
