import AWS from "aws-sdk";

// Configure AWS SDK
AWS.config.update({
    region: process.env.AWS_REGION || "us-east-1",
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
});

const cognito = new AWS.CognitoIdentityServiceProvider();

export interface CognitoUser {
    userId: string;
    email: string;
    username: string;
    name?: string;
}

export interface AuthResult {
    success: boolean;
    user?: CognitoUser;
    tokens?: {
        accessToken: string;
        idToken: string;
        refreshToken?: string;
    };
    error?: string;
    requiresVerification?: boolean;
}

export class CognitoService {
    private static userPoolId =
        process.env.NEXT_PUBLIC_QBRAID_COGNITO_USER_POOL_ID;
    private static clientId = process.env.NEXT_PUBLIC_QBRAID_COGNITO_CLIENTID;

    /**
     * Authenticate user with email and password
     */
    static async authenticateUser(
        email: string,
        password: string
    ): Promise<AuthResult> {
        try {
            // For development: if <PERSON><PERSON><PERSON> is not configured, use mock authentication
            if (
                !this.userPoolId ||
                !this.clientId ||
                this.clientId === "your-client-id"
            ) {
                console.log(
                    "🔧 [COGNITO] Using mock authentication for development"
                );

                // Mock successful authentication
                return {
                    success: true,
                    user: {
                        userId: `mock-${email.split("@")[0] || "user"}`,
                        email: email,
                        username: email.split("@")[0] || "user",
                    },
                    tokens: {
                        accessToken: `mock-access-token-${Date.now()}`,
                        idToken: `mock-id-token-${Date.now()}`,
                        refreshToken: `mock-refresh-token-${Date.now()}`,
                    },
                };
            }

            if (!this.userPoolId || !this.clientId) {
                throw new Error("Cognito configuration missing");
            }

            const params = {
                AuthFlow: "USER_PASSWORD_AUTH",
                ClientId: this.clientId,
                AuthParameters: {
                    USERNAME: email,
                    PASSWORD: password,
                },
            };

            const result = await cognito.initiateAuth(params).promise();

            if (result.AuthenticationResult) {
                // Get user details
                const userDetails = await this.getUserDetails(email);

                // Extract tokens from authentication result
                const tokens = {
                    accessToken: result.AuthenticationResult.AccessToken!,
                    idToken: result.AuthenticationResult.IdToken!,
                    refreshToken: result.AuthenticationResult.RefreshToken,
                };

                return {
                    success: true,
                    user: userDetails,
                    tokens,
                };
            }

            // Handle challenge responses
            if (result.ChallengeName === "NEW_PASSWORD_REQUIRED") {
                return {
                    success: false,
                    error: "Password reset required",
                };
            }

            if (
                result.ChallengeName === "SMS_MFA" ||
                result.ChallengeName === "SOFTWARE_TOKEN_MFA"
            ) {
                return {
                    success: false,
                    error: "MFA verification required",
                };
            }

            return {
                success: false,
                error: "Authentication failed",
            };
        } catch (error: any) {
            console.error("Cognito authentication error:", error);

            // Handle specific Cognito errors
            if (error.code === "NotAuthorizedException") {
                return {
                    success: false,
                    error: "Invalid email or password",
                };
            }

            if (error.code === "UserNotConfirmedException") {
                return {
                    success: false,
                    requiresVerification: true,
                    error: "Please verify your email address",
                };
            }

            if (error.code === "UserNotFoundException") {
                return {
                    success: false,
                    error: "Invalid email or password",
                };
            }

            if (error.code === "TooManyRequestsException") {
                return {
                    success: false,
                    error: "Too many attempts. Please try again later.",
                };
            }

            return {
                success: false,
                error: error.message || "Authentication failed",
            };
        }
    }

    /**
     * Get user details from Cognito
     */
    private static async getUserDetails(email: string): Promise<CognitoUser> {
        try {
            const params = {
                UserPoolId: this.userPoolId!,
                Username: email,
            };

            const result = await cognito.adminGetUser(params).promise();

            // Extract user attributes
            const attributes = result.UserAttributes || [];
            const emailAttr = attributes.find((attr) => attr.Name === "email");
            const nameAttr = attributes.find((attr) => attr.Name === "name");
            const usernameAttr = attributes.find(
                (attr) => attr.Name === "preferred_username"
            );

            return {
                userId: result.Username || email,
                email: emailAttr?.Value || email,
                username: usernameAttr?.Value || email,
                name: nameAttr?.Value,
            };
        } catch (error) {
            console.error("Error getting user details:", error);
            // Return basic user info if we can't get details
            return {
                userId: email,
                email: email,
                username: email,
            };
        }
    }

    /**
     * Create a new user in Cognito
     */
    static async createUser(
        email: string,
        password: string,
        name: string
    ): Promise<AuthResult> {
        try {
            if (!this.userPoolId) {
                throw new Error("Cognito configuration missing");
            }

            const params = {
                UserPoolId: this.userPoolId,
                Username: email,
                UserAttributes: [
                    {
                        Name: "email",
                        Value: email,
                    },
                    {
                        Name: "name",
                        Value: name,
                    },
                    {
                        Name: "preferred_username",
                        Value: email,
                    },
                ],
                TemporaryPassword: password,
                MessageAction: "SEND",
            };

            await cognito.adminCreateUser(params).promise();

            return {
                success: true,
                user: {
                    userId: email,
                    email: email,
                    username: email,
                    name: name,
                },
            };
        } catch (error: any) {
            console.error("Cognito user creation error:", error);

            if (error.code === "UsernameExistsException") {
                return {
                    success: false,
                    error: "An account with this email already exists",
                };
            }

            return {
                success: false,
                error: error.message || "User creation failed",
            };
        }
    }

    /**
     * Confirm user signup
     */
    static async confirmSignUp(
        email: string,
        confirmationCode: string
    ): Promise<AuthResult> {
        try {
            if (!this.clientId) {
                throw new Error("Cognito configuration missing");
            }

            const params = {
                ClientId: this.clientId,
                Username: email,
                ConfirmationCode: confirmationCode,
            };

            await cognito.confirmSignUp(params).promise();

            return {
                success: true,
            };
        } catch (error: any) {
            console.error("Cognito confirmation error:", error);

            if (error.code === "CodeMismatchException") {
                return {
                    success: false,
                    error: "Invalid verification code",
                };
            }

            if (error.code === "ExpiredCodeException") {
                return {
                    success: false,
                    error: "Verification code has expired",
                };
            }

            return {
                success: false,
                error: error.message || "Confirmation failed",
            };
        }
    }

    /**
     * Initiate password reset
     */
    static async initiatePasswordReset(email: string): Promise<AuthResult> {
        try {
            if (!this.clientId) {
                throw new Error("Cognito configuration missing");
            }

            const params = {
                ClientId: this.clientId,
                Username: email,
            };

            await cognito.forgotPassword(params).promise();

            return {
                success: true,
            };
        } catch (error: any) {
            console.error("Cognito password reset error:", error);

            if (error.code === "UserNotFoundException") {
                return {
                    success: false,
                    error: "User not found",
                };
            }

            return {
                success: false,
                error: error.message || "Password reset failed",
            };
        }
    }

    /**
     * Complete password reset
     */
    static async completePasswordReset(
        email: string,
        confirmationCode: string,
        newPassword: string
    ): Promise<AuthResult> {
        try {
            if (!this.clientId) {
                throw new Error("Cognito configuration missing");
            }

            const params = {
                ClientId: this.clientId,
                Username: email,
                ConfirmationCode: confirmationCode,
                Password: newPassword,
            };

            await cognito.confirmForgotPassword(params).promise();

            return {
                success: true,
            };
        } catch (error: any) {
            console.error("Cognito password reset completion error:", error);

            if (error.code === "CodeMismatchException") {
                return {
                    success: false,
                    error: "Invalid verification code",
                };
            }

            if (error.code === "ExpiredCodeException") {
                return {
                    success: false,
                    error: "Verification code has expired",
                };
            }

            if (error.code === "InvalidPasswordException") {
                return {
                    success: false,
                    error: "Password does not meet requirements",
                };
            }

            return {
                success: false,
                error: error.message || "Password reset failed",
            };
        }
    }
}
