import { getSession } from "@/lib/auth/session";
import { redirect } from "next/navigation";

/**
 * Require authentication for a route
 * Redirects to sign-in page if user is not authenticated
 */
export async function requireAuth(redirectTo: string = "/signin") {
    const session = await getSession();

    if (!session?.signedIn) {
        redirect(redirectTo);
    }

    return session;
}

/**
 * Require no authentication for a route
 * Redirects to dashboard if user is already authenticated
 * Used for authentication pages (signin, signup, etc.)
 */
export async function requireNoAuth(redirectTo: string = "/dashboard") {
    const session = await getSession();

    if (session?.signedIn) {
        redirect(redirectTo);
    }

    return null;
}

/**
 * Get current authenticated user or null
 */
export async function getCurrentUser() {
    const session = await getSession();
    return session?.signedIn ? session : null;
}

/**
 * Check if user has specific permission
 */
export async function requirePermission(
    _permission: string,
    _redirectTo: string = "/"
) {
    const session = await getSession();

    if (!session?.signedIn) {
        redirect("/signin");
    }

    // For now, we'll assume the user has permission
    // In a real implementation, you would check against role mappings
    return session;
}
