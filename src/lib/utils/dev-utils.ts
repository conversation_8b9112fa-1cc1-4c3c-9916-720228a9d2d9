// Development environment utilities
// This file helps with common development setup issues

export const isDevelopment = process.env.NODE_ENV === "development";

/**
 * Development environment health checks
 */
export async function checkDevEnvironment() {
    if (!isDevelopment) return { ok: true };

    const issues: string[] = [];

    // Check Redis
    try {
        const redis = await import("@/lib/redis/redis");
        await redis.testRedisConnection();
    } catch {
        issues.push("Redis is not running. Start with: redis-server");
    }

    // Check external API
    try {
        const response = await fetch("http://localhost:3001/api/health");
        if (!response.ok) {
            issues.push("External API server is not responding properly");
        }
    } catch {
        issues.push("External API server is not running on localhost:3001");
    }

    return {
        ok: issues.length === 0,
        issues,
        message:
            issues.length > 0
                ? `Development setup issues:\n${issues.map((i) => `• ${i}`).join("\n")}`
                : "Development environment is healthy",
    };
}
