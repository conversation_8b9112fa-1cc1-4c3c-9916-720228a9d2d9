import Redis from "ioredis";
import { isDevelopment } from "./dev-utils";

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (request: Request) => string; // Custom key generator
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  onLimitReached?: (key: string, limit: RateLimitInfo) => void; // Callback when limit is reached
}

export interface RateLimitInfo {
  totalHits: number;
  remainingHits: number;
  resetTime: Date;
  windowMs: number;
}

export class RateLimiter {
  private redis: Redis | null = null;
  private inMemoryStore: Map<string, { count: number; resetTime: number }> =
    new Map();
  private options: Required<RateLimitOptions>;

  constructor(options: RateLimitOptions) {
    this.options = {
      keyGenerator: (request: Request) => this.getDefaultKey(request),
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      onLimitReached: (key, limit) => {
        if (isDevelopment) {
          console.warn(`Rate limit exceeded for key: ${key}`, limit);
        }
      },
      ...options,
    };

    // Initialize Redis if available
    if (process.env.REDIS_URL) {
      try {
        this.redis = new Redis({
          ...this.parseRedisUrl(process.env.REDIS_URL),
          maxRetriesPerRequest: 3,
        });
      } catch (error) {
        console.error("Failed to initialize Redis for rate limiting:", error);
      }
    }
  }

  private parseRedisUrl(url: string): {
    host: string;
    port: number;
    password?: string;
  } {
    const parsed = new URL(url);
    return {
      host: parsed.hostname,
      port: Number.parseInt(parsed.port) || 6379,
      password: parsed.password || undefined,
    };
  }

  private getDefaultKey(request: Request): string {
    // Try to get real IP
    const forwarded = request.headers.get("x-forwarded-for");
    const realIp = request.headers.get("x-real-ip");
    const ip = forwarded?.split(",")[0] || realIp || "unknown";

    // Add user agent for more specific limiting
    const userAgent = request.headers.get("user-agent") || "unknown";

    // For authenticated requests, include user ID
    const authHeader = request.headers.get("authorization");

    if (authHeader && authHeader.startsWith("Bearer ")) {
      // This would typically contain a JWT with user info
      return `user:${ip}:${userAgent}`;
    }

    return `ip:${ip}:${userAgent}`;
  }

  async checkRateLimit(request: Request): Promise<{
    allowed: boolean;
    limit: RateLimitInfo;
    headers: Record<string, string>;
  }> {
    const key = this.options.keyGenerator(request);
    const now = Date.now();
    const windowStart = now - this.options.windowMs;
    const resetTime = new Date(now + this.options.windowMs);

    if (this.redis) {
      return this.checkRedisRateLimit(key, now, windowStart, resetTime);
    } else {
      return this.checkInMemoryRateLimit(key, now, windowStart, resetTime);
    }
  }

  private async checkRedisRateLimit(
    key: string,
    now: number,
    windowStart: number,
    resetTime: Date,
  ): Promise<{
    allowed: boolean;
    limit: RateLimitInfo;
    headers: Record<string, string>;
  }> {
    if (!this.redis) {
      throw new Error("Redis not available");
    }

    try {
      // Use Redis pipeline for atomic operations
      const pipeline = this.redis.pipeline();

      // Remove old entries
      pipeline.zremrangebyscore(key, 0, windowStart);

      // Add current request
      pipeline.zadd(key, now, `${now}-${Math.random()}`);

      // Set expiration
      pipeline.expire(key, Math.ceil(this.options.windowMs / 1000));

      // Get count
      pipeline.zcard(key);

      const results = await pipeline.exec();

      if (!results || !results[3] || results[3][0]) {
        throw new Error("Redis operation failed");
      }

      const totalHits = results[3][1] as number;
      const remainingHits = Math.max(0, this.options.maxRequests - totalHits);

      const limit: RateLimitInfo = {
        totalHits,
        remainingHits,
        resetTime,
        windowMs: this.options.windowMs,
      };

      const allowed = totalHits <= this.options.maxRequests;

      if (!allowed && this.options.onLimitReached) {
        this.options.onLimitReached(key, limit);
      }

      return {
        allowed,
        limit,
        headers: this.getRateLimitHeaders(limit),
      };
    } catch (error) {
      console.error(
        "Redis rate limiting failed, falling back to in-memory:",
        error,
      );
      return this.checkInMemoryRateLimit(key, now, windowStart, resetTime);
    }
  }

  private checkInMemoryRateLimit(
    key: string,
    now: number,
    windowStart: number,
    resetTime: Date,
  ): {
    allowed: boolean;
    limit: RateLimitInfo;
    headers: Record<string, string>;
  } {
    const stored = this.inMemoryStore.get(key);

    // Clean up expired entries
    if (stored && stored.resetTime < now) {
      this.inMemoryStore.delete(key);
    }

    const count = stored ? stored.count : 0;
    const newCount = count + 1;

    // Update store
    this.inMemoryStore.set(key, {
      count: newCount,
      resetTime: now + this.options.windowMs,
    });

    const totalHits = newCount;
    const remainingHits = Math.max(0, this.options.maxRequests - totalHits);

    const limit: RateLimitInfo = {
      totalHits,
      remainingHits,
      resetTime,
      windowMs: this.options.windowMs,
    };

    const allowed = totalHits <= this.options.maxRequests;

    if (!allowed && this.options.onLimitReached) {
      this.options.onLimitReached(key, limit);
    }

    return {
      allowed,
      limit,
      headers: this.getRateLimitHeaders(limit),
    };
  }

  private getRateLimitHeaders(limit: RateLimitInfo): Record<string, string> {
    return {
      "X-RateLimit-Limit": limit.windowMs.toString(),
      "X-RateLimit-Remaining": limit.remainingHits.toString(),
      "X-RateLimit-Reset": limit.resetTime.toISOString(),
      "Retry-After": Math.ceil(
        (limit.resetTime.getTime() - Date.now()) / 1000,
      ).toString(),
    };
  }

  // Cleanup old entries periodically
  startCleanup() {
    if (!this.redis) {
      setInterval(() => {
        const now = Date.now();
        for (const [key, value] of this.inMemoryStore.entries()) {
          if (value.resetTime < now) {
            this.inMemoryStore.delete(key);
          }
        }
      }, 60000); // Clean up every minute
    }
  }

  // Reset rate limit for a key
  async reset(key: string): Promise<void> {
    if (this.redis) {
      await this.redis.del(key);
    } else {
      this.inMemoryStore.delete(key);
    }
  }

  // Get current rate limit info for a key
  async getCurrentLimit(key: string): Promise<RateLimitInfo | null> {
    if (this.redis) {
      try {
        const ttl = await this.redis.ttl(key);
        if (ttl > 0) {
          const count = await this.redis.zcard(key);
          return {
            totalHits: count,
            remainingHits: Math.max(0, this.options.maxRequests - count),
            resetTime: new Date(Date.now() + ttl * 1000),
            windowMs: this.options.windowMs,
          };
        }
      } catch (error) {
        console.error("Failed to get current limit from Redis:", error);
      }
    }

    const stored = this.inMemoryStore.get(key);
    if (stored && stored.resetTime > Date.now()) {
      return {
        totalHits: stored.count,
        remainingHits: Math.max(0, this.options.maxRequests - stored.count),
        resetTime: new Date(stored.resetTime),
        windowMs: this.options.windowMs,
      };
    }

    return null;
  }
}

// Pre-configured rate limiters for different scenarios
export const authRateLimiters = {
  // General authentication attempts (5 per minute)
  auth: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  }),

  // Password reset attempts (3 per hour)
  passwordReset: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
  }),

  // Sign up attempts (3 per hour per IP)
  signUp: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
  }),

  // API requests (100 per minute)
  api: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
  }),

  // OAuth callback attempts (10 per minute)
  oauth: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
  }),
};

// Start cleanup for in-memory store
authRateLimiters.auth.startCleanup();

// Middleware helper for Next.js API routes
export async function applyRateLimit(
  request: Request,
  limiter: RateLimiter,
): Promise<Response | null> {
  const result = await limiter.checkRateLimit(request);

  if (!result.allowed) {
    return new Response(
      JSON.stringify({
        error: "Too many requests",
        message: "Rate limit exceeded. Please try again later.",
        retryAfter: result.headers["Retry-After"],
      }),
      {
        status: 429,
        headers: {
          "Content-Type": "application/json",
          ...result.headers,
        },
      },
    );
  }

  // Add rate limit headers to successful responses
  return null;
}
