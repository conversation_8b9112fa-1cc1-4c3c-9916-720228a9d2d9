## Authentication System

This document summarizes how authentication, sessions, roles/permissions, and middleware work in this codebase.

### Overview

- **Primary auth provider**: Amazon Cognito (email/password or tokens)
- **Session storage**: Redis-backed session IDs (preferred) with JWT fallback
- **Tokens**: Cognito `accessToken` and `idToken` stored in Redis (preferred) or secure cookies (fallback)
- **Protection**: Next.js middleware for route guarding; rate limiting on auth endpoints; CSRF utilities for server actions
- **Authorization**: Role- and permission-based checks resolved server-side and consumable on client hooks

### Key Modules (by responsibility)

- Sessions & cookies: `src/lib/auth/session.ts`
- Cognito integration: `src/lib/auth/cognito-service.ts`
- User profile fetch: `src/lib/auth/user-service.ts`
- Auth helpers (SSR): `src/lib/auth/auth.ts`
- Middleware (routing): `src/middleware.ts`
- Permissions & roles: `src/lib/permissions/permissions.ts`, `src/types/auth.ts`, `src/lib/external-roles.ts`
- Token storage (Redis): `src/lib/redis/redis-token-storage.ts`, `src/lib/redis/redis.ts`
- Rate limiting: `src/lib/utils/rate-limiter.ts`
- Client auth context: `src/components/auth/auth-provider.tsx`
- Server actions: `src/app/(auth)/actions.ts`

### Authentication Flows

1. Direct Cognito sign-in (email/password)
    - Endpoint: `src/app/api/auth/signin/route.ts` (POST)
    - Validates input (zod) → logs attempt → `CognitoService.authenticateUser(email, password)`
    - Fetches user profile via `UserService.getUserProfile(...)` (with mock fallback during development)
    - Creates session via `createSession(...)` and sets session cookie via `setSessionCookie(...)`
    - Persists Cognito tokens via `setCognitoTokenCookies(...)`

2. Token-based sign-in (Cognito/OAuth done on client)
    - Same endpoint accepts `{ idToken, accessToken, refreshToken? }`
    - Validates tokens, decodes ID token for identity, optionally fetches profile
    - Creates session and persists tokens as above

3. Sign-up & Forgot Password
    - `src/app/api/auth/signup/route.ts` and `src/app/api/auth/forgot-password/route.ts` are placeholders
    - Both apply rate limiting and structured logging; wire to provider as needed

### Session Lifecycle

- Create: `createSession(userData)` in `session.ts`
    - Generates a `sessionId` and `SessionPayload` with `permissions`, `roles`, `organizationId`, `iat`, `exp`.
    - Prefers Redis: stores payload at `REDIS_SESSION_PREFIX + sessionId` with TTL.
    - Fallback: signs a JWT (HS256) embedding the same payload.
- Verify: `verifySession(token)`
    - If token matches session ID pattern and Redis is healthy → load payload from Redis (exp checked)
    - If token is JWT → verify with `SESSION_SECRET`, ensure required fields
    - Edge fallback: if session ID but no Redis (edge runtime), returns a minimal authenticated payload
- Retrieve current: `getSession()` reads `SESSION_COOKIE_NAME` and calls `verifySession(...)`
- Refresh: `refreshSession(currentSession)` and `validateAndRefreshSession()` optionally rotate/extend
- Cookies: `setSessionCookie(sessionToken)` stores either the session ID or JWT using strict, httpOnly, secure options

### Cognito Token Handling

- Prefer Redis: `setCognitoTokenCookies(tokens, sessionId)` writes to `REDIS_COGNITO_PREFIX + sessionId`
- Fallback to cookies when Redis unavailable: writes `ACCESS_TOKEN_COOKIE_NAME` and `ID_TOKEN_COOKIE_NAME`
- Retrieval: `getCognitoTokenCookies(sessionId?)` checks Redis first, then cookies
- Clearing: `clearCognitoTokenCookies()` removes from Redis/cookies

### Middleware & Routing

- `src/middleware.ts` guards routes at the edge:
    - Public paths: `/signin`, `/signup`, `/forgot-password`, `/reset-password`, `/verify`, `/oauth`, `/accept-invite`
    - If `!session?.signedIn` and path is protected → redirect to `/signin`
    - If signed in and hitting `/signin` → redirect to `/dashboard`

### Client Integration

- `AuthProvider` (`src/components/auth/auth-provider.tsx`) exposes user state and refresh capability
- Hooks: `src/hooks/use-permissions.ts` provides helpers to evaluate permissions on the client
- Forms (examples):
    - Server action `authenticateUser` (`src/app/(auth)/actions.ts`) posts to `/api/auth/signin` with CSRF validation and logs audit events

### User Profile & External API Integration

- **UserService** (`src/lib/auth/user-service.ts`) fetches user context from external API:
    - Complete context: `GET {EXTERNAL_API_URL}/api/v1/users/:email/context`
    - Organization-specific: `GET {EXTERNAL_API_URL}/api/v1/users/:email/organizations/:orgId/context`
    - Headers: `Authorization: Bearer <idToken>`, optional `X-Organization-ID: <orgId>`
- **Response Format**: `{ success: true, data: { user, organizations, permissions, roles, currentOrganizationId } }`
- **Organization Map**: Organizations returned as object map keyed by orgId, containing roles and custom permissions
- **Permission Aggregation**: Combines permissions from direct user permissions and organization-specific permissions
- **Fallback**: Returns mock data when external API unavailable (development mode)

### Authorization Model

- Role and permission strings (types and resolvers): `src/types/auth.ts`, `src/lib/permissions/permissions.ts`
- Effective permissions = user permissions ∪ permissions implied by roles
- Server-side middleware helpers (Express-style) are provided for API-like contexts: `requirePermission`, `requireAnyPermission`, `requireAllPermissions`, `requireRole`, `requireAnyRole`, `requirePermissionsOrRoles`
- External/organization roles resolution and caching: `src/lib/external-roles.ts`

### Security Controls

- CSRF: token utilities and validation used in server actions (see `actions.ts`)
- Rate limiting: `authRateLimiters` and `applyRateLimit` guard sign-in/sign-up/reset endpoints
- Cookies: `httpOnly`, `secure` in production, `sameSite: strict`, domain set by env in production
- Secrets/config: `SESSION_SECRET`, cookie names, TTLs, Redis prefixes in `src/lib/utils/constant.ts`
- Logging: `auth-logger.ts` records attempts, failures, and success events with request context

### Environment & Configuration

- Ensure the following are configured (examples; see `src/lib/utils/constant.ts` and `.env`):
    - `SESSION_SECRET` (HS256 signing key)
    - `COOKIE_DOMAIN` (prod)
    - `STORAGE_MODE` and Redis connection vars if using Redis
    - `NEXT_PUBLIC_APP_URL` for server actions calling API routes

### Minimal API Usage Examples

Sign in (email/password):

```bash
curl -X POST "$APP_URL/api/auth/signin" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"secret"}'
```

Sign in (tokens):

```bash
curl -X POST "$APP_URL/api/auth/signin" \
  -H "Content-Type: application/json" \
  -d '{"idToken":"<ID_TOKEN>","accessToken":"<ACCESS_TOKEN>"}'
```

### Extensibility Notes

- Implement registration and password reset by replacing placeholders in their routes and reusing logging/rate-limiter patterns
- To add providers, normalize user identity → create session → persist provider tokens (prefer Redis) → set session cookie
- For new protected routes, rely on `middleware.ts` and optionally perform permission checks server-side using helpers
