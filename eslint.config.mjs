import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Keep it simple for now - the foundation is more important
      "prefer-const": "off", // Temporarily disable
      "no-var": "error",
      // 'no-console': ['warn', { allow: ['warn', 'error'] }],
      "no-debugger": "error",
      "@typescript-eslint/no-explicit-any": "off", // Temporarily disable for development
      "@typescript-eslint/no-unused-vars": "off", // Temporarily disable
      "react/no-unescaped-entities": "off", // Temporarily disable
      "react-hooks/exhaustive-deps": "off", // Temporarily disable
    },
  },
  {
    ignores: [
      "node_modules/**",
      ".next/**",
      "out/**",
      "build/**",
      "dist/**",
      "next-env.d.ts",
      "*.config.js",
      "*.config.mjs",
      "*.config.ts",
      "coverage/**",
      ".turbo/**",
    ],
  },
];

export default eslintConfig;
