
> qbraid-account@0.1.0 dev
> next dev --turbopack

   ▲ Next.js 15.5.3 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://************:3000
   - Environments: .env
   - Experiments (use with caution):
     · optimizePackageImports

 ✓ Starting...
 ✓ Compiled middleware in 126ms
 ✓ Ready in 895ms
 ○ Compiling /api/auth/signin ...
 ✓ Compiled /api/auth/signin in 39.4s
(node:40703) NOTE: The AWS SDK for JavaScript (v2) is in maintenance mode.
 SDK releases are limited to address critical bug fixes and security issues only.

Please migrate your code to use AWS SDK for JavaScript (v3).
For more information, check the blog post at https://a.co/cUPnyil
(Use `node --trace-warnings ...` to show where the warning was created)
[Auth Event] {
  "id": "auth_1757909770107_unatly0z4",
  "timestamp": "2025-09-15T04:16:10.107Z",
  "eventType": "SIGN_IN_FAILURE",
  "email": "unknown",
  "ipAddress": "::1",
  "userAgent": "curl/8.7.1",
  "success": false,
  "error": "Unexpected end of JSON input"
}
[Auth Event] {
  "id": "auth_1757909770867_h5yw1w563",
  "timestamp": "2025-09-15T04:16:10.867Z",
  "eventType": "SIGN_IN_ATTEMPT",
  "email": "<EMAIL>",
  "ipAddress": "::1",
  "userAgent": "curl/8.7.1",
  "success": false
}
[UserService] Requesting user context {
  url: 'http://localhost:3001/api/v1/users/alex%40qbraid.com/context',
  emailUsed: '<EMAIL>',
  identifier: '<EMAIL>',
  hasIdToken: true,
  idTokenPreview: 'eyJraWQiOiIw...(1035)',
  organizationId: undefined,
  headers: {
    'Content-Type': 'application/json',
    Authorization: 'Bearer <redacted>',
    'X-Organization-ID': undefined
  }
}
[UserService] Context API success {
  hasDataWrapper: true,
  id: '68bb2353a030bb8afa3ecdfd',
  email: '<EMAIL>',
  orgs: undefined,
  roles: undefined,
  permissions: 8
}
🔌 [REDIS] Creating new Redis connection: redis://localhost:6379
✅ [REDIS] Connected to Redis server
🚀 [REDIS] Redis client ready
📊 [REDIS] Server version: 8.0.2
🍪 [SESSION] Setting session cookie: {
  name: 'session',
  value: 'eyJhbGci...',
  options: {
    httpOnly: true,
    secure: false,
    sameSite: 'strict',
    maxAge: 86400,
    path: '/',
    domain: 'localhost'
  }
}
🔐 [SESSION] Cognito tokens stored securely in Redis
[Auth Event] {
  "id": "auth_1757909772963_7ax9x9s1s",
  "timestamp": "2025-09-15T04:16:12.963Z",
  "eventType": "SIGN_IN_SUCCESS",
  "userId": "757241bd-d757-4d1c-9230-7be1259273ee",
  "email": "<EMAIL>",
  "ipAddress": "::1",
  "userAgent": "curl/8.7.1",
  "success": true
}
 POST /api/auth/signin 200 in 2899ms
 ✓ Compiled middleware in 446ms
 ✓ Compiled in 754ms
 ✓ Compiled middleware in 285ms
 ✓ Compiled in 6.2s
 ✓ Compiled middleware in 11ms
 ✓ Compiled in 169ms
 ✓ Compiled middleware in 1ms
 ✓ Compiled middleware in 5ms
 ✓ Compiled middleware in 18ms
 ✓ Compiled in 103ms
 ✓ Compiled middleware in 1ms
 ✓ Compiled in 43ms
 ✓ Compiled middleware in 4ms
 ✓ Compiled in 95ms
 ✓ Compiled middleware in 4ms
 ✓ Compiled in 63ms
