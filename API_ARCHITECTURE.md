# API Architecture Documentation

## Overview

This document describes the architecture of the qBraid account system, which uses Next.js as a full-stack framework providing both frontend and backend capabilities. The system integrates with an external Express API for core business logic.

### Architecture Layers

1. **Frontend (Client-Side)**
   - **Technology**: React components within Next.js
   - **Location**: User's browser
   - **Purpose**: UI rendering, user interactions
   - **Auth**: Session cookie (httpOnly)

2. **Backend (Server-Side)**
   - **Technology**: Next.js API Routes
   - **Location**: Same Next.js app (`localhost:3000/api/*`)
   - **Purpose**: Session management, API gateway, security layer
   - **Auth**: Manages Redis sessions and token retrieval

3. **External API**
   - **Technology**: Express.js
   - **Location**: Separate service (`localhost:3001`)
   - **Purpose**: Core business logic, data persistence
   - **Auth**: Bearer token (Cognito ID token)

## Architecture Flow Diagram

```
┌────────────────────────────────────────────────┐
│              Next.js Application               │
│                                                │
│  ┌──────────────────┐    ┌──────────────────┐ │         ┌─────────────────┐
│  │    Frontend      │    │    Backend       │ │         │                 │
│  │  (Client-Side)   │    │  (Server-Side)   │ │         │  Express API    │
│  │                  │    │                  │ │         │ (localhost:3001)│
│  │ • React Pages    │    │ • API Routes     │ │         │                 │
│  │ • Components     │───▶│ • /api/auth/*    │─────────▶│ • User Context  │
│  │ • Hooks          │    │ • /api/user/*    │ │         │ • Permissions   │
│  │ • UI State       │    │ • /api/org/*     │ │         │ • Business Logic│
│  │                  │    │                  │ │         │ • MongoDB       │
│  │ Uses: apiClient  │    │ Uses: external   │ │         │                 │
│  │                  │    │       Client     │ │         └─────────────────┘
│  └──────────────────┘    └──────────────────┘ │                 ▲
│           │                        │           │                 │
│           │ Session Cookie        │           │                 │
│           │ (httpOnly)            │           │          Bearer Token
│           ▼                        ▼           │          (ID Token)
│  ┌──────────────────────────────────────────┐ │                 │
│  │              Middleware                   │ │                 │
│  │  • Route Protection                       │ │                 │
│  │  • Session Validation                     │ │                 │
│  └──────────────────────────────────────────┘ │                 │
│                                                │                 │
└────────────────────────────────────────────────┘                 │
                        │                                           │
                        │                                           │
                        ▼                                           │
                ┌──────────────┐                                   │
                │    Redis     │                                   │
                │              │                                   │
                │ • Sessions   │───────────────────────────────────┘
                │ • Tokens     │
                │ • User Cache │
                └──────────────┘
```

### Why Next.js for Both Frontend and Backend?

1. **Unified Codebase**: Single deployment, shared types, consistent development experience
2. **Security**: Backend API routes act as a secure proxy, keeping tokens server-side
3. **Performance**: Server-side rendering, API route caching, optimized builds
4. **Developer Experience**: Hot reload for both frontend and backend, integrated tooling

## Request Flow Example

### 1. User Login Flow

```typescript
// Step 1: UI Component initiates login
// File: src/components/auth/sign-in-form.tsx
const handleSignIn = async (credentials) => {
  console.log('[UI] Initiating sign-in for:', credentials.email);

  // Calls localhost:3000/api/auth/signin
  const response = await authAPI.signIn({
    email: credentials.email,
    password: credentials.password
  });

  console.log('[UI] Sign-in response:', response);
};

// Step 2: Next.js API Route handles the request
// File: src/app/api/auth/signin/route.ts
export async function POST(request: NextRequest) {
  console.log('[NEXT-API] Sign-in request received');

  // Authenticate with Cognito
  const authResult = await CognitoService.authenticateUser(email, password);
  console.log('[NEXT-API] Cognito auth result:', authResult.success);

  // Call Express API for user context
  console.log('[NEXT-API] Fetching user context from Express API');
  const userProfile = await UserService.getUserProfile(
    authResult.user.userId,
    authResult.tokens.idToken,
    undefined,
    authResult.user.email
  );

  // Create session in Redis
  const sessionId = await createSession(userData, tokens, userContext);
  console.log('[NEXT-API] Session created:', sessionId);

  // Set httpOnly cookie
  await setSessionCookie(sessionId);
  console.log('[NEXT-API] Session cookie set');
}

// Step 3: External API Client makes request to Express
// File: src/lib/auth/user-service.ts
static async getUserProfile() {
  console.log('[UserService] Requesting user context', {
    url: 'http://localhost:3001/api/v1/users/email/context',
    hasIdToken: true,
    organizationId: organizationId
  });

  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${idToken}`,
      'X-Organization-ID': organizationId
    }
  });

  console.log('[UserService] Context API response:', response.status);
}
```

## Session Management

### Redis Structure

```
session:{sessionId}
├── userId: string
├── email: string
├── permissions: string[]
├── roles: string[]
├── organizationId: string
└── exp: number

session:{sessionId}:cognito
├── idToken: string
├── accessToken: string
└── storedAt: string

user:{email}:context
├── user: UserProfile
├── organizations: OrgMap
├── permissions: string[]
├── roles: string[]
└── lastUpdated: string
```

## API Clients

### 1. Client-Side API Client (`apiClient`)

**Purpose**: Used by React components to call Next.js API routes

```typescript
// File: src/lib/api/api-client.ts
class APIClient {
    constructor() {
        console.log("[API-CLIENT] Initializing client-side API client");
        this.client = axios.create({
            baseURL: "/api", // Calls Next.js API routes
            withCredentials: true, // Include session cookie
        });
    }

    async post(url: string, data?: any) {
        console.log(`[API-CLIENT] POST ${url}`, {
            hasOrgId: !!this.organizationId,
            dataKeys: Object.keys(data || {}),
        });

        // Session cookie is automatically included
        // Organization ID is added to request body if set
        if (this.organizationId && data) {
            data.organizationId = this.organizationId;
        }

        const response = await this.client.post(url, data);
        console.log(`[API-CLIENT] Response ${url}:`, response.status);
        return response.data;
    }
}
```

### 2. External API Client (`externalClient`)

**Purpose**: Used by Next.js API routes to call Express API

```typescript
// File: src/lib/api/external-client.ts
class ExternalAPIClient {
    constructor() {
        console.log("[EXTERNAL-CLIENT] Initializing external API client");
        this.client = axios.create({
            baseURL: "http://localhost:3001/api", // Express API
        });
    }

    // Request interceptor adds auth headers
    private setupInterceptors() {
        this.client.interceptors.request.use(async (config) => {
            // Get session from cookie
            const session = await getSession();
            console.log("[EXTERNAL-CLIENT] Session found:", !!session);

            if (session?.jti) {
                // Get ID token from Redis
                const tokens = await getCognitoTokensBySessionId(session.jti);
                console.log(
                    "[EXTERNAL-CLIENT] Tokens retrieved:",
                    !!tokens?.idToken
                );

                if (tokens?.idToken) {
                    config.headers["Authorization"] =
                        `Bearer ${tokens.idToken}`;
                    console.log(
                        "[EXTERNAL-CLIENT] Added Bearer token to request"
                    );
                }

                if (session.organizationId) {
                    config.headers["X-Organization-ID"] =
                        session.organizationId;
                    console.log(
                        "[EXTERNAL-CLIENT] Added org ID:",
                        session.organizationId
                    );
                }
            }

            return config;
        });
    }
}
```

## Complete Request Flow with Logs

### Example: Fetching User Profile

```typescript
// 1. React Component
function ProfilePage() {
    useEffect(() => {
        console.log("[PROFILE-PAGE] Component mounted, fetching profile");
        fetchProfile();
    }, []);

    const fetchProfile = async () => {
        try {
            console.log("[PROFILE-PAGE] Calling API...");
            // This calls localhost:3000/api/user/profile
            const response = await userAPI.getProfile();
            console.log("[PROFILE-PAGE] Profile received:", response);
        } catch (error) {
            console.error("[PROFILE-PAGE] Error:", error);
        }
    };
}

// 2. Client API Service
// File: src/lib/api/client/user-api.ts
export const userAPI = {
    async getProfile() {
        console.log("[USER-API-CLIENT] Getting user profile");
        // Calls Next.js backend at localhost:3000
        return apiClient.get("/user/profile");
    },
};

// 3. Next.js API Route
// File: src/app/api/user/profile/route.ts
export async function GET(request: NextRequest) {
    console.log("[PROFILE-API] Profile request received");

    // Get session from cookie
    const session = await getSession();
    console.log("[PROFILE-API] Session valid:", !!session?.signedIn);

    // Check Redis cache first
    const cached = await getUserContextByEmail(session.email);
    if (cached) {
        console.log("[PROFILE-API] Returning cached profile");
        return NextResponse.json({ source: "cache", data: cached });
    }

    // Call Express API (automatic token injection)
    console.log("[PROFILE-API] Fetching from Express API...");
    const profile = await userAPI.getProfile(session.email);
    console.log("[PROFILE-API] Express API responded");

    return NextResponse.json({ source: "external-api", data: profile });
}

// 4. External API Service
// File: src/lib/api/user-api.ts
export const userAPI = {
    async getProfile(email: string) {
        console.log("[USER-API-EXTERNAL] Fetching profile for:", email);
        // externalClient automatically adds Bearer token
        // This calls localhost:3001/api/v1/users/{email}/context
        return externalClient.get(
            `/v1/users/${encodeURIComponent(email)}/context`
        );
    },
};
```

## Security Flow

### Authentication Headers

```
Client → Next.js Backend:
- Cookie: session=<sessionId> (httpOnly, secure, sameSite=strict)

Next.js Backend → Express API:
- Authorization: Bearer <idToken>
- X-Organization-ID: <organizationId> (optional)
- Content-Type: application/json
```

### Session Validation Flow

```typescript
// 1. Middleware validates session
// File: src/middleware.ts
export async function middleware(request: NextRequest) {
    console.log("[MIDDLEWARE] Checking session for:", request.url);

    const session = await getSession();
    console.log("[MIDDLEWARE] Session valid:", !!session?.signedIn);

    if (!session?.signedIn && !isPublicPath) {
        console.log("[MIDDLEWARE] Redirecting to signin");
        return NextResponse.redirect("/signin");
    }
}

// 2. Session verification
// File: src/lib/auth/session.ts
export async function getSession() {
    console.log("[SESSION] Retrieving session from cookie");

    const cookieValue = cookies().get(SESSION_COOKIE_NAME)?.value;
    console.log("[SESSION] Cookie found:", !!cookieValue);

    if (!cookieValue) return null;

    const session = await verifySession(cookieValue);
    console.log("[SESSION] Session verified:", !!session);

    return session;
}
```

## Error Handling

### Client-Side Error Handling

```typescript
// File: src/lib/api/api-client.ts
this.client.interceptors.response.use(
    (response) => {
        console.log(`✅ [API] Success: ${response.config.url}`);
        return response;
    },
    async (error: AxiosError) => {
        console.error(`❌ [API] Error: ${error.config?.url}`, {
            status: error.response?.status,
            message: error.message,
            data: error.response?.data,
        });

        // Handle session expiry
        if (error.response?.status === 401) {
            console.log("🔄 [API] Session expired, redirecting to login");
            window.location.href = "/signin";
        }

        return Promise.reject(error);
    }
);
```

### Server-Side Error Handling

```typescript
// File: src/lib/api/external-client.ts
this.client.interceptors.response.use(
    (response) => {
        console.log(`✅ [External API] Success: ${response.config.url}`);
        return response;
    },
    (error: AxiosError) => {
        console.error(`❌ [External API] Error:`, {
            url: error.config?.url,
            status: error.response?.status,
            data: error.response?.data,
            headers: error.config?.headers,
        });

        // Log specific auth errors
        if (error.response?.status === 401) {
            console.error(
                "🔒 [External API] Authentication failed - token may be expired"
            );
        }

        return Promise.reject(error);
    }
);
```

## Testing the Flow

### 1. Test Login Flow

```bash
# Watch logs in terminal
tail -f logs/api.log

# Test login
curl -X POST http://localhost:3000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Expected logs:
# [NEXT-API] Sign-in request received
# [NEXT-API] Cognito auth result: true
# [UserService] Requesting user context
# [UserService] Context API response: 200
# [NEXT-API] Session created: abc123...
# [NEXT-API] Session cookie set
```

### 2. Test Profile Fetch

```bash
# With session cookie
curl http://localhost:3000/api/user/profile \
  -H "Cookie: session=<sessionId>"

# Expected logs:
# [PROFILE-API] Profile request received
# [SESSION] Retrieving session from cookie
# [SESSION] Session verified: true
# [PROFILE-API] Fetching from Express API...
# [EXTERNAL-CLIENT] Session found: true
# [EXTERNAL-CLIENT] Tokens retrieved: true
# [EXTERNAL-CLIENT] Added Bearer token to request
# [External API] GET /v1/users/<EMAIL>/context - 200
# [PROFILE-API] Express API responded
```

## Development Tips

1. **Enable Debug Logs**:

    ```typescript
    // In .env.local
    NEXT_PUBLIC_DEBUG = true;
    LOG_LEVEL = debug;
    ```

2. **Monitor Redis**:

    ```bash
    redis-cli monitor
    ```

3. **Check Network Tab**:
    - Client → Next.js: Look for session cookie
    - Next.js → Express: Look for Bearer token in logs

4. **Common Issues**:
    - Missing session cookie: Check `withCredentials: true`
    - 401 errors: Check Redis for valid tokens
    - CORS errors: Ensure Express allows Next.js origin

## Environment Variables

```bash
# .env.local
EXTERNAL_API_URL=http://localhost:3001
REDIS_URL=redis://localhost:6379
SESSION_COOKIE_NAME=session
SESSION_DURATION_SECONDS=86400
NEXT_PUBLIC_DEBUG=true
```
